/**
 * Test script for task management functionality
 * This tests the core CRUD operations to ensure they work with Supabase
 */

import { 
  createEnhancedTodo, 
  getEnhancedTodos, 
  updateEnhancedTodo, 
  deleteEnhancedTodo 
} from '../utils/enhancedSupabase';
import { EnhancedTodoItem } from '../types/todo';

// Test data
const testUserId = 'test-user-id';

const testTask: Partial<EnhancedTodoItem> = {
  title: 'Test Task',
  description: 'This is a test task to verify CRUD operations',
  priority: 'medium',
  difficultyLevel: 'medium',
  tags: ['test', 'crud'],
  chapterTags: ['testing'],
  completionPercentage: 0,
  timeEstimate: 60,
  notes: 'Test notes for the task',
  columnId: 'column-1',
};

export async function runTaskManagementTests() {
  console.log('🧪 Starting Task Management Tests...');
  
  try {
    // Test 1: Create Task
    console.log('📝 Test 1: Creating task...');
    const createdTask = await createEnhancedTodo(testTask);
    console.log('✅ Task created successfully:', createdTask.id);
    
    // Test 2: Read Tasks
    console.log('📖 Test 2: Reading tasks...');
    const tasks = await getEnhancedTodos(testUserId);
    console.log('✅ Tasks retrieved successfully:', tasks.length, 'tasks found');
    
    // Test 3: Update Task
    console.log('✏️ Test 3: Updating task...');
    const updatedTask = await updateEnhancedTodo(createdTask.id, {
      title: 'Updated Test Task',
      completionPercentage: 50,
      notes: 'Updated notes',
    });
    console.log('✅ Task updated successfully:', updatedTask.title);
    
    // Test 4: Delete Task
    console.log('🗑️ Test 4: Deleting task...');
    await deleteEnhancedTodo(createdTask.id);
    console.log('✅ Task deleted successfully');
    
    // Test 5: Verify Deletion
    console.log('🔍 Test 5: Verifying deletion...');
    const tasksAfterDeletion = await getEnhancedTodos(testUserId);
    const deletedTaskExists = tasksAfterDeletion.find(t => t.id === createdTask.id);
    
    if (!deletedTaskExists) {
      console.log('✅ Task deletion verified - task no longer exists');
    } else {
      console.log('❌ Task deletion failed - task still exists');
    }
    
    console.log('🎉 All tests completed successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

// Advanced test scenarios
export async function runAdvancedTaskTests() {
  console.log('🚀 Starting Advanced Task Tests...');
  
  try {
    // Test hierarchical tasks
    console.log('🌳 Testing hierarchical tasks...');
    
    const parentTask = await createEnhancedTodo({
      ...testTask,
      title: 'Parent Task',
    });
    
    const childTask = await createEnhancedTodo({
      ...testTask,
      title: 'Child Task',
      parentId: parentTask.id,
    });
    
    console.log('✅ Hierarchical tasks created successfully');
    
    // Test bulk operations
    console.log('📦 Testing bulk operations...');
    
    const task1 = await createEnhancedTodo({
      ...testTask,
      title: 'Bulk Task 1',
    });
    
    const task2 = await createEnhancedTodo({
      ...testTask,
      title: 'Bulk Task 2',
    });
    
    console.log('✅ Bulk tasks created successfully');
    
    // Cleanup
    await deleteEnhancedTodo(parentTask.id, true); // Delete with subtasks
    await deleteEnhancedTodo(task1.id);
    await deleteEnhancedTodo(task2.id);
    
    console.log('✅ Advanced tests completed successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Advanced test failed:', error);
    return false;
  }
}

// Export test runner
export default {
  runTaskManagementTests,
  runAdvancedTaskTests,
};
