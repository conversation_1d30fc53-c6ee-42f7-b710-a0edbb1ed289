import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { useSupabaseSubjectStore } from '@/stores/supabaseSubjectStore';
import { EnhancedTodoItem } from '@/types/todo';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>rk<PERSON>ine<PERSON>hart,
  Gauge,
} from '@mui/x-charts';
import {
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle2,
  AlertTriangle,
  Target,
  Calendar,
  BarChart3,
  <PERSON><PERSON>hart as PieChartIcon,
  Activity,
  Award,
  Zap,
  Filter,
  Sparkles,
  Timer,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { format, subDays, startOfDay, endOfDay, isWithinInterval } from 'date-fns';
import { cn } from '@/lib/utils';

interface TaskAnalyticsDashboardProps {
  isOpen: boolean;
  onClose: () => void;
}

interface AnalyticsData {
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  completionRate: number;
  averageCompletionTime: number;
  productivityScore: number;
  taskVelocity: number;
  subjectDistribution: Array<{ name: string; value: number; color: string }>;
  priorityDistribution: Array<{ name: string; value: number; color: string }>;
  difficultyDistribution: Array<{ name: string; value: number; color: string }>;
  weeklyProgress: Array<{ date: string; completed: number; created: number }>;
  monthlyTrends: Array<{ month: string; completion: number; velocity: number }>;
}

const COLORS = {
  primary: '#8b5cf6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#3b82f6',
  purple: '#a855f7',
  pink: '#ec4899',
  indigo: '#6366f1',
};

// MIUI-inspired theme for charts with dark mode support
const createMuiTheme = (isDark: boolean) => createTheme({
  palette: {
    mode: isDark ? 'dark' : 'light',
    primary: {
      main: '#8b5cf6',
    },
    secondary: {
      main: '#10b981',
    },
    background: {
      default: isDark ? '#0a0a0a' : '#ffffff',
      paper: isDark ? '#1a1a1a' : '#ffffff',
    },
    text: {
      primary: isDark ? '#ffffff' : '#000000',
      secondary: isDark ? '#a1a1aa' : '#6b7280',
    },
  },
});

export function TaskAnalyticsDashboard({ isOpen, onClose }: TaskAnalyticsDashboardProps) {
  const { getFilteredTasks } = useEnhancedTodoStore();
  const { subjects: userSubjects } = useSupabaseSubjectStore();
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  const [selectedView, setSelectedView] = useState<'overview' | 'subjects' | 'performance'>('overview');

  // Dark mode detection
  const isDarkMode = document.documentElement.classList.contains('dark');
  const muiTheme = useMemo(() => createMuiTheme(isDarkMode), [isDarkMode]);

  const allTasks = getFilteredTasks();

  // Filter tasks by time range
  const filteredTasks = useMemo(() => {
    if (timeRange === 'all') return allTasks;

    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const startDate = startOfDay(subDays(new Date(), days));
    const endDate = endOfDay(new Date());

    return allTasks.filter(task =>
      isWithinInterval(new Date(task.createdAt), { start: startDate, end: endDate })
    );
  }, [allTasks, timeRange]);

  // Calculate analytics data
  const analyticsData = useMemo((): AnalyticsData => {
    const totalTasks = filteredTasks.length;
    const completedTasks = filteredTasks.filter(task => task.completionPercentage === 100).length;
    const overdueTasks = filteredTasks.filter(task =>
      task.dueDate && new Date(task.dueDate) < new Date() && task.completionPercentage < 100
    ).length;

    const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

    // Calculate average completion time (for completed tasks)
    const completedTasksWithTime = filteredTasks.filter(task =>
      task.completionPercentage === 100 && task.actualTimeSpent
    );
    const averageCompletionTime = completedTasksWithTime.length > 0
      ? completedTasksWithTime.reduce((sum, task) => sum + (task.actualTimeSpent || 0), 0) / completedTasksWithTime.length
      : 0;

    // Calculate productivity score (0-100)
    const productivityScore = Math.min(100, Math.round(
      (completionRate * 0.4) +
      (Math.max(0, 100 - (overdueTasks / Math.max(totalTasks, 1)) * 100) * 0.3) +
      (Math.min(100, (completedTasks / Math.max(1, timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90)) * 10) * 0.3)
    ));

    // Calculate task velocity (tasks completed per day)
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : Math.max(1, (Date.now() - Math.min(...filteredTasks.map(t => t.createdAt))) / (1000 * 60 * 60 * 24));
    const taskVelocity = completedTasks / Math.max(1, days);

    // Subject distribution
    const subjectCounts = filteredTasks.reduce((acc, task) => {
      const subject = task.subjectId ? userSubjects.find(s => s.id === task.subjectId) : null;
      const subjectName = subject ? subject.name : 'No Subject';
      const subjectColor = subject ? subject.color : '#6b7280';

      if (!acc[subjectName]) {
        acc[subjectName] = { count: 0, color: subjectColor };
      }
      acc[subjectName].count++;
      return acc;
    }, {} as Record<string, { count: number; color: string }>);

    const subjectDistribution = Object.entries(subjectCounts).map(([name, data]) => ({
      name,
      value: data.count,
      color: data.color,
    }));

    // Priority distribution
    const priorityDistribution = [
      {
        name: 'High',
        value: filteredTasks.filter(t => t.priority === 'high').length,
        color: COLORS.danger
      },
      {
        name: 'Medium',
        value: filteredTasks.filter(t => t.priority === 'medium').length,
        color: COLORS.warning
      },
      {
        name: 'Low',
        value: filteredTasks.filter(t => t.priority === 'low').length,
        color: COLORS.success
      },
    ].filter(item => item.value > 0);

    // Difficulty distribution
    const difficultyDistribution = [
      {
        name: 'Hard',
        value: filteredTasks.filter(t => t.difficultyLevel === 'hard').length,
        color: COLORS.danger
      },
      {
        name: 'Medium',
        value: filteredTasks.filter(t => t.difficultyLevel === 'medium').length,
        color: COLORS.warning
      },
      {
        name: 'Easy',
        value: filteredTasks.filter(t => t.difficultyLevel === 'easy').length,
        color: COLORS.success
      },
    ].filter(item => item.value > 0);

    // Weekly progress (last 7 days)
    const weeklyProgress = Array.from({ length: 7 }, (_, i) => {
      const date = subDays(new Date(), 6 - i);
      const dayStart = startOfDay(date);
      const dayEnd = endOfDay(date);

      const dayTasks = allTasks.filter(task =>
        isWithinInterval(new Date(task.createdAt), { start: dayStart, end: dayEnd })
      );

      const completedInDay = allTasks.filter(task =>
        task.completionPercentage === 100 &&
        task.updatedAt &&
        isWithinInterval(new Date(task.updatedAt), { start: dayStart, end: dayEnd })
      );

      return {
        date: format(date, 'MMM dd'),
        completed: completedInDay.length,
        created: dayTasks.length,
      };
    });

    // Monthly trends (placeholder - would need more historical data)
    const monthlyTrends = [
      { month: 'Jan', completion: 85, velocity: 2.3 },
      { month: 'Feb', completion: 78, velocity: 2.1 },
      { month: 'Mar', completion: 92, velocity: 2.8 },
      { month: 'Apr', completion: completionRate, velocity: taskVelocity },
    ];

    return {
      totalTasks,
      completedTasks,
      overdueTasks,
      completionRate,
      averageCompletionTime,
      productivityScore,
      taskVelocity,
      subjectDistribution,
      priorityDistribution,
      difficultyDistribution,
      weeklyProgress,
      monthlyTrends,
    };
  }, [filteredTasks, userSubjects, timeRange, allTasks]);

  const StatCard = ({ title, value, subtitle, icon: Icon, trend, color = 'primary', sparkData }: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: any;
    trend?: 'up' | 'down' | 'neutral';
    color?: keyof typeof COLORS;
    sparkData?: number[];
  }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ y: -2 }}
    >
      <Card className="bg-card/80 backdrop-blur-sm border shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
        <CardContent className="p-6 relative">
          {/* Background gradient */}
          <div className={cn(
            "absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300",
            color === 'primary' && "bg-gradient-to-br from-purple-500 to-violet-600",
            color === 'success' && "bg-gradient-to-br from-emerald-500 to-green-600",
            color === 'warning' && "bg-gradient-to-br from-amber-500 to-orange-600",
            color === 'info' && "bg-gradient-to-br from-blue-500 to-cyan-600"
          )} />

          <div className="flex items-center justify-between relative z-10">
            <div className="flex-1">
              <p className="text-sm font-medium text-muted-foreground mb-1">{title}</p>
              <p className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                {value}
              </p>
              {subtitle && (
                <p className="text-xs text-muted-foreground mt-1">{subtitle}</p>
              )}
            </div>

            <div className="flex flex-col items-end gap-2">
              <div className={cn(
                "p-3 rounded-2xl shadow-lg transition-all duration-300 group-hover:scale-110",
                color === 'primary' && "bg-gradient-to-br from-purple-500 to-violet-600",
                color === 'success' && "bg-gradient-to-br from-emerald-500 to-green-600",
                color === 'warning' && "bg-gradient-to-br from-amber-500 to-orange-600",
                color === 'info' && "bg-gradient-to-br from-blue-500 to-cyan-600"
              )}>
                <Icon className="h-6 w-6 text-white" />
              </div>

              {sparkData && sparkData.length > 0 && (
                <div className="w-16 h-8">
                  <ThemeProvider theme={muiTheme}>
                    <SparkLineChart
                      data={sparkData}
                      width={64}
                      height={32}
                      curve="linear"
                      colors={[COLORS[color]]}
                    />
                  </ThemeProvider>
                </div>
              )}
            </div>
          </div>

          {trend && (
            <motion.div
              className="flex items-center mt-4 relative z-10"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {trend === 'up' ? (
                <TrendingUp className="h-4 w-4 text-emerald-500 mr-2" />
              ) : trend === 'down' ? (
                <TrendingDown className="h-4 w-4 text-red-500 mr-2" />
              ) : null}
              <span className={cn(
                "text-sm font-medium",
                trend === 'up' ? 'text-emerald-600 dark:text-emerald-400' :
                  trend === 'down' ? 'text-red-600 dark:text-red-400' :
                    'text-gray-600 dark:text-gray-400'
              )}>
                {trend === 'up' ? 'Improving' : trend === 'down' ? 'Declining' : 'Stable'}
              </span>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );

  const renderOverview = () => (
    <motion.div
      className="space-y-8"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6, staggerChildren: 0.1 }}
    >
      {/* Key Metrics */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <StatCard
          title="Total Tasks"
          value={analyticsData.totalTasks}
          icon={Target}
          color="primary"
          sparkData={analyticsData.weeklyProgress.map(d => d.created)}
        />
        <StatCard
          title="Completion Rate"
          value={`${Math.round(analyticsData.completionRate)}%`}
          subtitle={`${analyticsData.completedTasks} completed`}
          icon={CheckCircle}
          trend={analyticsData.completionRate > 75 ? 'up' : analyticsData.completionRate > 50 ? 'neutral' : 'down'}
          color="success"
          sparkData={analyticsData.weeklyProgress.map(d => d.completed)}
        />
        <StatCard
          title="Productivity Score"
          value={analyticsData.productivityScore}
          subtitle="Based on completion & timing"
          icon={Sparkles}
          trend={analyticsData.productivityScore > 75 ? 'up' : analyticsData.productivityScore > 50 ? 'neutral' : 'down'}
          color="warning"
        />
        <StatCard
          title="Task Velocity"
          value={analyticsData.taskVelocity.toFixed(1)}
          subtitle="tasks per day"
          icon={Activity}
          color="info"
        />
      </motion.div>

      {/* Productivity Score Gauge */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
          <CardHeader className="border-b pb-4">
            <CardTitle className="text-xl font-semibold flex items-center gap-3">
              <div className="p-2 rounded-xl bg-gradient-to-br from-purple-500 to-violet-600">
                <Award className="h-5 w-5 text-white" />
              </div>
              <span>Productivity Overview</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="flex flex-col items-center">
                <ThemeProvider theme={muiTheme}>
                  <div className="relative">
                    <svg width="0" height="0">
                      <defs>
                        <linearGradient id="productivityGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                          <stop offset="0%" stopColor="#8b5cf6" />
                          <stop offset="100%" stopColor="#06b6d4" />
                        </linearGradient>
                      </defs>
                    </svg>
                    <Gauge
                      width={200}
                      height={200}
                      value={analyticsData.productivityScore}
                      startAngle={-90}
                      endAngle={90}
                      innerRadius="70%"
                      outerRadius="100%"
                      cornerRadius="50%"
                      sx={{
                        '& .MuiGauge-valueText': {
                          fontSize: '2rem',
                          fontWeight: 'bold',
                          fill: isDarkMode ? '#ffffff' : '#8b5cf6',
                        },
                        '& .MuiGauge-valueArc': {
                          fill: 'url(#productivityGradient)',
                        },
                        '& .MuiGauge-referenceArc': {
                          fill: isDarkMode ? '#374151' : '#e5e7eb',
                        },
                      }}
                    />
                  </div>
                </ThemeProvider>
                <p className="text-lg font-semibold mt-4">Productivity Score</p>
                <p className="text-sm text-muted-foreground">Out of 100</p>
              </div>

              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Task Completion</span>
                    <span className="text-sm text-muted-foreground">{Math.round(analyticsData.completionRate)}%</span>
                  </div>
                  <Progress value={analyticsData.completionRate} className="h-2" />
                </div>

                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">On-time Delivery</span>
                    <span className="text-sm text-muted-foreground">
                      {Math.max(0, 100 - (analyticsData.overdueTasks / Math.max(analyticsData.totalTasks, 1)) * 100).toFixed(0)}%
                    </span>
                  </div>
                  <Progress
                    value={Math.max(0, 100 - (analyticsData.overdueTasks / Math.max(analyticsData.totalTasks, 1)) * 100)}
                    className="h-2"
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Daily Velocity</span>
                    <span className="text-sm text-muted-foreground">{analyticsData.taskVelocity.toFixed(1)} tasks/day</span>
                  </div>
                  <Progress value={Math.min(100, analyticsData.taskVelocity * 20)} className="h-2" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Weekly Progress */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
            <CardHeader className="border-b pb-4">
              <CardTitle className="text-lg font-semibold flex items-center gap-3">
                <div className="p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600">
                  <BarChart3 className="h-5 w-5 text-white" />
                </div>
                <span>Weekly Progress</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="h-[300px]">
                <ThemeProvider theme={muiTheme}>
                  <BarChart
                    dataset={analyticsData.weeklyProgress}
                    xAxis={[{ scaleType: 'band', dataKey: 'date' }]}
                    series={[
                      {
                        dataKey: 'completed',
                        label: 'Completed',
                        color: '#10b981',
                      },
                      {
                        dataKey: 'created',
                        label: 'Created',
                        color: '#8b5cf6',
                      },
                    ]}
                    width={undefined}
                    height={300}
                    margin={{ left: 40, right: 40, top: 40, bottom: 40 }}
                  />
                </ThemeProvider>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Priority Distribution */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
            <CardHeader className="border-b pb-4">
              <CardTitle className="text-lg font-semibold flex items-center gap-3">
                <div className="p-2 rounded-xl bg-gradient-to-br from-pink-500 to-rose-600">
                  <PieChartIcon className="h-5 w-5 text-white" />
                </div>
                <span>Priority Distribution</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="h-[300px]">
                <ThemeProvider theme={muiTheme}>
                  <PieChart
                    series={[
                      {
                        data: analyticsData.priorityDistribution.map((item, index) => ({
                          id: index,
                          value: item.value,
                          label: item.name,
                          color: item.color,
                        })),
                        highlightScope: { faded: 'global', highlighted: 'item' },
                        faded: { innerRadius: 30, additionalRadius: -30, color: 'gray' },
                      },
                    ]}
                    width={undefined}
                    height={300}
                    margin={{ right: 200 }}
                  />
                </ThemeProvider>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Alerts */}
      <AnimatePresence>
        {analyticsData.overdueTasks > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="border-amber-200/50 dark:border-amber-800/50 bg-gradient-to-r from-amber-50/80 to-orange-50/80 dark:from-amber-950/20 dark:to-orange-950/20 backdrop-blur-sm shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-gradient-to-br from-amber-500 to-orange-600">
                    <AlertCircle className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="font-semibold text-amber-800 dark:text-amber-200 text-lg">
                      {analyticsData.overdueTasks} overdue task{analyticsData.overdueTasks > 1 ? 's' : ''}
                    </p>
                    <p className="text-sm text-amber-700 dark:text-amber-300 mt-1">
                      Consider reviewing and updating deadlines or priorities to improve your productivity score
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-amber-300 text-amber-700 hover:bg-amber-100"
                  >
                    Review Tasks
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );



  const renderSubjects = () => (
    <motion.div
      className="space-y-8"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
          <CardHeader className="border-b pb-4">
            <CardTitle className="text-xl font-semibold flex items-center gap-3">
              <div className="p-2 rounded-xl bg-gradient-to-br from-violet-500 to-purple-600">
                <Target className="h-5 w-5 text-white" />
              </div>
              <span>Subject Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Enhanced Pie Chart */}
              <div className="h-[350px]">
                <ThemeProvider theme={muiTheme}>
                  <PieChart
                    series={[
                      {
                        data: analyticsData.subjectDistribution.map((item, index) => ({
                          id: index,
                          value: item.value,
                          label: item.name,
                          color: item.color,
                        })),
                        highlightScope: { faded: 'global', highlighted: 'item' },
                        faded: { innerRadius: 30, additionalRadius: -30, color: 'gray' },
                        innerRadius: 40,
                        outerRadius: 120,
                        paddingAngle: 2,
                        cornerRadius: 5,
                      },
                    ]}
                    width={undefined}
                    height={350}
                    margin={{ right: 100 }}
                  />
                </ThemeProvider>
              </div>

              {/* Subject List with Enhanced Design */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold mb-4">Subject Breakdown</h3>
                {analyticsData.subjectDistribution.map((subject, index) => (
                  <motion.div
                    key={index}
                    className="group"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.3 }}
                  >
                    <div className="flex items-center justify-between p-4 bg-card/60 backdrop-blur-sm rounded-xl border hover:shadow-md transition-all duration-300 group-hover:scale-[1.02]">
                      <div className="flex items-center gap-4">
                        <div
                          className="w-5 h-5 rounded-full shadow-lg"
                          style={{ backgroundColor: subject.color }}
                        />
                        <div>
                          <span className="font-semibold text-base">{subject.name}</span>
                          <p className="text-sm text-muted-foreground">
                            {((subject.value / analyticsData.totalTasks) * 100).toFixed(1)}% of total
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge
                          variant="secondary"
                          className="bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 font-semibold"
                        >
                          {subject.value} tasks
                        </Badge>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Subject Performance Comparison */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
          <CardHeader className="border-b pb-4">
            <CardTitle className="text-xl font-semibold flex items-center gap-3">
              <div className="p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600">
                <BarChart3 className="h-5 w-5 text-white" />
              </div>
              <span>Subject Performance</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="h-[300px]">
              <ThemeProvider theme={muiTheme}>
                <BarChart
                  dataset={analyticsData.subjectDistribution}
                  xAxis={[{ scaleType: 'band', dataKey: 'name' }]}
                  series={[
                    {
                      dataKey: 'value',
                      label: 'Tasks',
                      color: '#8b5cf6',
                    },
                  ]}
                  width={undefined}
                  height={300}
                  margin={{ left: 40, right: 40, top: 40, bottom: 60 }}
                />
              </ThemeProvider>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );

  const renderPerformance = () => (
    <motion.div
      className="space-y-8"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Difficulty Distribution */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
            <CardHeader className="border-b pb-4">
              <CardTitle className="text-lg font-semibold flex items-center gap-3">
                <div className="p-2 rounded-xl bg-gradient-to-br from-red-500 to-pink-600">
                  <AlertTriangle className="h-5 w-5 text-white" />
                </div>
                <span>Task Difficulty</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="h-[300px]">
                <ThemeProvider theme={muiTheme}>
                  <BarChart
                    dataset={analyticsData.difficultyDistribution}
                    xAxis={[{ scaleType: 'band', dataKey: 'name' }]}
                    series={[
                      {
                        dataKey: 'value',
                        label: 'Tasks',
                        color: '#8b5cf6',
                      },
                    ]}
                    width={undefined}
                    height={300}
                    margin={{ left: 40, right: 40, top: 40, bottom: 60 }}
                  />
                </ThemeProvider>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Performance Insights */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1, duration: 0.5 }}
        >
          <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
            <CardHeader className="border-b pb-4">
              <CardTitle className="text-lg font-semibold flex items-center gap-3">
                <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600">
                  <Award className="h-5 w-5 text-white" />
                </div>
                <span>Performance Metrics</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-6 space-y-6">
              <motion.div
                className="space-y-3"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.3 }}
              >
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Productivity Score</span>
                  <span className="text-sm font-bold text-purple-600">{analyticsData.productivityScore}/100</span>
                </div>
                <Progress value={analyticsData.productivityScore} className="h-3" />
              </motion.div>

              <motion.div
                className="space-y-3"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.3 }}
              >
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Completion Rate</span>
                  <span className="text-sm font-bold text-emerald-600">{Math.round(analyticsData.completionRate)}%</span>
                </div>
                <Progress value={analyticsData.completionRate} className="h-3" />
              </motion.div>

              <motion.div
                className="space-y-3"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.3 }}
              >
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Task Velocity</span>
                  <span className="text-sm font-bold text-blue-600">{analyticsData.taskVelocity.toFixed(1)} tasks/day</span>
                </div>
                <Progress value={Math.min(100, analyticsData.taskVelocity * 20)} className="h-3" />
              </motion.div>

              {analyticsData.averageCompletionTime > 0 && (
                <motion.div
                  className="pt-4 border-t"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.3 }}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <Timer className="h-4 w-4 text-muted-foreground" />
                    <p className="text-sm font-medium text-muted-foreground">Average Completion Time</p>
                  </div>
                  <p className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                    {Math.floor(analyticsData.averageCompletionTime / 60)}h {analyticsData.averageCompletionTime % 60}m
                  </p>
                </motion.div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Performance Insights Cards */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <Card className="bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-950/20 dark:to-green-950/20 border-emerald-200 dark:border-emerald-800">
          <CardContent className="p-6 text-center">
            <div className="p-3 rounded-full bg-emerald-500 w-fit mx-auto mb-4">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
            <h3 className="font-semibold text-emerald-800 dark:text-emerald-200 mb-2">Completed Tasks</h3>
            <p className="text-3xl font-bold text-emerald-600 dark:text-emerald-400">{analyticsData.completedTasks}</p>
            <p className="text-sm text-emerald-600 dark:text-emerald-400 mt-1">
              {((analyticsData.completedTasks / Math.max(analyticsData.totalTasks, 1)) * 100).toFixed(1)}% of total
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950/20 dark:to-orange-950/20 border-amber-200 dark:border-amber-800">
          <CardContent className="p-6 text-center">
            <div className="p-3 rounded-full bg-amber-500 w-fit mx-auto mb-4">
              <Clock className="h-6 w-6 text-white" />
            </div>
            <h3 className="font-semibold text-amber-800 dark:text-amber-200 mb-2">Overdue Tasks</h3>
            <p className="text-3xl font-bold text-amber-600 dark:text-amber-400">{analyticsData.overdueTasks}</p>
            <p className="text-sm text-amber-600 dark:text-amber-400 mt-1">
              {((analyticsData.overdueTasks / Math.max(analyticsData.totalTasks, 1)) * 100).toFixed(1)}% of total
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-200 dark:border-blue-800">
          <CardContent className="p-6 text-center">
            <div className="p-3 rounded-full bg-blue-500 w-fit mx-auto mb-4">
              <Activity className="h-6 w-6 text-white" />
            </div>
            <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Active Tasks</h3>
            <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {analyticsData.totalTasks - analyticsData.completedTasks}
            </p>
            <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
              Currently in progress
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden bg-background/95 backdrop-blur-xl border shadow-2xl">
        <DialogHeader className="border-b pb-4">
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-3 text-2xl font-bold">
              <div className="p-2 rounded-xl bg-gradient-to-br from-purple-500 to-violet-600">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <span className="bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                Task Analytics Dashboard
              </span>
            </DialogTitle>
            <div className="flex items-center gap-3">
              <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
                <SelectTrigger className="w-40 bg-card/80 backdrop-blur-sm border shadow-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="all">All time</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </DialogHeader>

        <div className="flex h-[75vh]">
          {/* Enhanced Sidebar */}
          <div className="w-56 border-r pr-6">
            <div className="space-y-2">
              {[
                { id: 'overview', label: 'Overview', icon: BarChart3, color: 'from-purple-500 to-violet-600' },
                { id: 'subjects', label: 'Subjects', icon: Target, color: 'from-emerald-500 to-green-600' },
                { id: 'performance', label: 'Performance', icon: Award, color: 'from-amber-500 to-orange-600' },
              ].map(({ id, label, icon: Icon, color }) => (
                <motion.div key={id} whileHover={{ x: 4 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    variant={selectedView === id ? 'default' : 'ghost'}
                    className={cn(
                      "w-full justify-start h-12 text-left transition-all duration-300",
                      selectedView === id
                        ? `bg-gradient-to-r ${color} text-white shadow-lg hover:shadow-xl`
                        : "hover:bg-card/80 hover:backdrop-blur-sm"
                    )}
                    onClick={() => setSelectedView(id as any)}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    <span className="font-medium">{label}</span>
                  </Button>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 pl-6 overflow-y-auto custom-scrollbar">
            <AnimatePresence mode="wait">
              <motion.div
                key={selectedView}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.4, ease: "easeInOut" }}
              >
                {selectedView === 'overview' && renderOverview()}
                {selectedView === 'subjects' && renderSubjects()}
                {selectedView === 'performance' && renderPerformance()}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
