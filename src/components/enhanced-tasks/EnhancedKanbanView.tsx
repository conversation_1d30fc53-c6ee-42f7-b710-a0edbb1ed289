import React, { useState, useMemo, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { DragDropContext, Droppable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Plus,
  MoreHorizontal,
  Settings,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Users,
  Filter,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { EnhancedTaskCard } from './EnhancedTaskCard';
import { TaskCreationModal } from './TaskCreationModal';
import { ColumnSettingsModal } from './ColumnSettingsModal';
import { EnhancedTodoItem } from '@/types/todo';

interface EnhancedKanbanViewProps {
  className?: string;
}

export const EnhancedKanbanView = React.memo(function EnhancedKanbanView({ className = '' }: EnhancedKanbanViewProps) {
  const { user } = useSupabaseAuth();
  const store = useEnhancedTodoStore();
  const {
    board = { tasks: {}, columns: {}, columnOrder: [] },
    moveTask,
    getFilteredTasks,
    subjects = {},
    search = { query: '', filters: {} },
    fetchTodos,
  } = store || {};

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [createTaskColumnId, setCreateTaskColumnId] = useState<string>('');
  const [groupBy, setGroupBy] = useState<'none' | 'subject' | 'priority'>('none');
  const [isColumnSettingsOpen, setIsColumnSettingsOpen] = useState(false);
  const [settingsColumnId, setSettingsColumnId] = useState<string>('');

  // Get filtered tasks - ensure it updates when board.tasks changes
  const filteredTasks = useMemo(() => {
    if (!getFilteredTasks || !board?.tasks) return {};
    try {
      const filtered = getFilteredTasks();
      if (!Array.isArray(filtered)) return {};
      return filtered.reduce((acc, task) => {
        if (task && task.id) {
          acc[task.id] = task;
        }
        return acc;
      }, {} as Record<string, EnhancedTodoItem>);
    } catch (error) {
      console.error('Error filtering tasks:', error);
      return {};
    }
  }, [getFilteredTasks, board?.tasks, search]);

  // Log board updates for debugging
  useEffect(() => {
    const tasksCount = Object.keys(board?.tasks || {}).length;
    console.log('Kanban view board updated:', {
      tasksCount,
      columnsCount: Object.keys(board?.columns || {}).length,
      filteredTasksCount: Object.keys(filteredTasks).length
    });
  }, [board?.tasks, board?.columns, filteredTasks]);

  // Group tasks by selected criteria - optimize with stable references
  const groupedColumns = useMemo(() => {
    if (!board?.columns || !filteredTasks) return [];
    
    const filteredTasksArray = Object.values(filteredTasks);
    
    if (groupBy === 'none') {
      // Standard column view - only recalculate when columns or filtered tasks change
      return Object.values(board.columns).map(column => {
        if (!column) return null;
        
        const columnTasks = (column.taskIds || [])
          .map(id => filteredTasks[id])
          .filter(Boolean);
        
        return {
          id: column.id,
          title: column.title,
          color: column.color,
          taskIds: column.taskIds || [],
          tasks: columnTasks,
        };
      }).filter(Boolean);
    }

    if (groupBy === 'subject') {
      // Group by subjects
      const subjectGroups: Record<string, EnhancedTodoItem[]> = {};

      filteredTasksArray.forEach(task => {
        const subjectId = task.subjectId || 'no-subject';
        if (!subjectGroups[subjectId]) {
          subjectGroups[subjectId] = [];
        }
        subjectGroups[subjectId].push(task);
      });

      return Object.entries(subjectGroups).map(([subjectId, tasks]) => {
        const subject = subjectId !== 'no-subject' ? subjects[subjectId] : null;
        return {
          id: `subject-${subjectId}`,
          title: subject ? subject.name : 'No Subject',
          color: subject?.color || '#6b7280',
          taskIds: tasks.map(t => t.id),
          tasks,
        };
      });
    }

    if (groupBy === 'priority') {
      // Group by priority
      const priorityGroups: Record<string, EnhancedTodoItem[]> = {
        high: [],
        medium: [],
        low: [],
      };

      filteredTasksArray.forEach(task => {
        priorityGroups[task.priority].push(task);
      });

      return Object.entries(priorityGroups).map(([priority, tasks]) => ({
        id: `priority-${priority}`,
        title: `${priority.charAt(0).toUpperCase() + priority.slice(1)} Priority`,
        color: priority === 'high' ? '#ef4444' : priority === 'medium' ? '#f59e0b' : '#10b981',
        taskIds: tasks.map(t => t.id),
        tasks,
      }));
    }

    return [];
  }, [board?.columns, filteredTasks, groupBy, subjects]);

  // Handle drag end
  const handleDragEnd = async (result: DropResult) => {
    const { destination, source, draggableId } = result;

    if (!destination || !moveTask) return;

    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    try {
      await moveTask(
        {
          droppableId: source.droppableId,
          index: source.index,
          taskId: draggableId,
        },
        {
          droppableId: destination.droppableId,
          index: destination.index,
        }
      );
    } catch (error) {
      console.error('Failed to move task:', error);
    }
  };

  // Handle create task in column
  const handleCreateTaskInColumn = (columnId: string) => {
    console.log('handleCreateTaskInColumn entered for column:', columnId);
    setCreateTaskColumnId(columnId);
    setIsCreateModalOpen(true);
  };

  // Handle column settings
  const handleColumnSettings = (columnId: string) => {
    setSettingsColumnId(columnId);
    setIsColumnSettingsOpen(true);
  };

  // Get column statistics
  const getColumnStats = (tasks: EnhancedTodoItem[]) => {
    const total = tasks.length;
    const completed = tasks.filter(task => task.completionPercentage === 100).length;
    const overdue = tasks.filter(task => {
      if (!task.dueDate) return false;
      return task.dueDate < Date.now() && task.completionPercentage < 100;
    }).length;

    return { total, completed, overdue };
  };

  // Column variants for animations
  const columnVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.3, ease: "easeOut" }
    },
  };

  const taskListVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <>
      <div className={`h-full ${className}`}>
        {/* Kanban Controls */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <h2 className="font-onest text-lg font-semibold text-gray-900 dark:text-white">
              {groupBy === 'none' ? 'Kanban Board' :
                groupBy === 'subject' ? 'Grouped by Subject' :
                  'Grouped by Priority'}
            </h2>

            {/* Group By Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-gray-300 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-600"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Group by: {groupBy === 'none' ? 'Status' : groupBy}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-white/95 dark:bg-[#030303]/95 backdrop-blur-md border-gray-200 dark:border-gray-800">
                <DropdownMenuItem
                  onClick={() => setGroupBy('none')}
                  className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-violet-500/20"
                >
                  Status (Default)
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setGroupBy('subject')}
                  className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-violet-500/20"
                >
                  Subject
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setGroupBy('priority')}
                  className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-violet-500/20"
                >
                  Priority
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Board Actions */}
          <div className="flex items-center gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="border-violet-500/50 text-violet-400 hover:bg-violet-500/20"
              onClick={(e) => {
                console.log('Main "Add Task" button onClick handler entered.');
                e.preventDefault();
                e.stopPropagation();
                console.log('Add Task button clicked');
                setIsCreateModalOpen(true);
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Task
            </Button>
          </div>
        </div>

        {/* Kanban Board */}
        <DragDropContext onDragEnd={handleDragEnd}>
          <div className="flex flex-col md:flex-row gap-4 md:gap-6 overflow-x-auto pb-6 items-start">
            {!groupedColumns || groupedColumns.length === 0 ? (
              <div className="flex items-center justify-center w-full h-64 text-gray-500">
                <div className="text-center">
                  <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg mb-2">Setting up your board...</p>
                  <p className="text-sm">Please wait while we load your tasks</p>
                </div>
              </div>
            ) : (
              <AnimatePresence>
                {groupedColumns.map((column) => {
                if (!column || !column.id) return null;
                
                const stats = getColumnStats(column.tasks || []);
                const completionRate = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;

                return (
                  <motion.div
                    key={column.id}
                    variants={columnVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                    className="flex-shrink-0 w-full md:w-80"
                  >
                    <Card className="bg-white/80 dark:bg-[#030303]/60 backdrop-blur-md border-gray-200/50 dark:border-gray-800/50 shadow-xl">
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            {/* Column color indicator */}
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: column.color }}
                            />

                            <div>
                              <CardTitle className="text-gray-900 dark:text-white font-onest text-base">
                                {column.title}
                              </CardTitle>
                              <div className="flex items-center gap-3 mt-1">
                                <span className="text-xs text-gray-600 dark:text-gray-400">
                                  {stats.total} task{stats.total !== 1 ? 's' : ''}
                                </span>
                                {stats.completed > 0 && (
                                  <Badge
                                    variant="outline"
                                    className="text-xs border-emerald-500/50 text-emerald-400 bg-emerald-500/10"
                                  >
                                    <CheckCircle2 className="h-3 w-3 mr-1" />
                                    {stats.completed}
                                  </Badge>
                                )}
                                {stats.overdue > 0 && (
                                  <Badge
                                    variant="outline"
                                    className="text-xs border-red-500/50 text-red-400 bg-red-500/10"
                                  >
                                    <AlertTriangle className="h-3 w-3 mr-1" />
                                    {stats.overdue}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Column Actions */}
                          <div className="flex items-center gap-1">
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-violet-500/20"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleCreateTaskInColumn(column.id);
                              }}
                            >
                              <Plus className="h-4 w-4" />
                            </Button>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-violet-500/20"
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent
                                align="end"
                                className="bg-white/95 dark:bg-[#030303]/95 backdrop-blur-md border-gray-200 dark:border-gray-800"
                              >
                                <DropdownMenuItem
                                  className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-violet-500/20"
                                  onClick={() => handleColumnSettings(column.id)}
                                >
                                  <Settings className="h-4 w-4 mr-2" />
                                  Column Settings
                                </DropdownMenuItem>
                                <DropdownMenuItem className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-violet-500/20">
                                  <Users className="h-4 w-4 mr-2" />
                                  Assign All Tasks
                                </DropdownMenuItem>
                                <DropdownMenuSeparator className="bg-gray-200 dark:bg-gray-800" />
                                <DropdownMenuItem className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-violet-500/20">
                                  Clear Completed
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>

                        {/* Progress Bar */}
                        {stats.total > 0 && (
                          <div className="mt-3">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-xs text-gray-400">Progress</span>
                              <span className="text-xs text-gray-300">{Math.round(completionRate)}%</span>
                            </div>
                            <Progress
                              value={completionRate}
                              className="h-1.5 bg-gray-800"
                              style={{
                                background: 'linear-gradient(90deg, rgba(139, 92, 246, 0.2) 0%, rgba(168, 85, 247, 0.2) 100%)'
                              }}
                            />
                          </div>
                        )}
                      </CardHeader>

                      <CardContent className="pt-0 pb-4 px-4">
                        <Droppable droppableId={column.id}>
                          {(provided, snapshot) => (
                            <motion.div
                              {...provided.droppableProps}
                              ref={provided.innerRef}
                              variants={taskListVariants}
                              initial="hidden"
                              animate="visible"
                              className={`
                                space-y-3 transition-all duration-200
                                ${snapshot.isDraggingOver
                                  ? 'bg-violet-500/5 border-2 border-dashed border-violet-500/30 rounded-lg p-2'
                                  : ''
                                }
                              `}
                              style={{
                                minHeight: column.tasks.length === 0 ? '120px' : 'auto',
                              }}
                            >
                              {!column.tasks || column.tasks.length === 0 ? (
                                <div className="flex flex-col items-center justify-center h-32 text-gray-500">
                                  <Clock className="h-8 w-8 mb-2 opacity-50" />
                                  <p className="text-sm">No tasks yet</p>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    className="mt-2 text-xs text-violet-400 hover:text-violet-300 hover:bg-violet-500/20"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      handleCreateTaskInColumn(column.id);
                                    }}
                                  >
                                    <Plus className="h-3 w-3 mr-1" />
                                    Add first task
                                  </Button>
                                </div>
                              ) : (
                                column.tasks.map((task, taskIndex) => {
                                  if (!task || !task.id) return null;
                                  return (
                                    <EnhancedTaskCard
                                      key={task.id}
                                      task={task}
                                      index={taskIndex}
                                      showSubtasks={false}
                                    />
                                  );
                                })
                              )}
                              {provided.placeholder}
                            </motion.div>
                          )}
                        </Droppable>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
                })}
              </AnimatePresence>
            )}
          </div>
        </DragDropContext>
      </div>

      {/* Task Creation Modal */}
      <TaskCreationModal
        isOpen={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          setCreateTaskColumnId('');
        }}
        columnId={createTaskColumnId}
      />

      {/* Column Settings Modal */}
      <ColumnSettingsModal
        isOpen={isColumnSettingsOpen}
        onClose={() => {
          setIsColumnSettingsOpen(false);
          setSettingsColumnId('');
        }}
        columnId={settingsColumnId}
      />
    </>
  );
});
