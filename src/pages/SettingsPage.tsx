import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { useTheme } from "@/hooks/use-theme";
import { useToast } from "@/components/ui/use-toast";
import {
  Moon, Sun, Trash2, Mail, User, ArrowLeft, Shield, Activity,
  Palette, Link as LinkIcon, X, ChevronRight, Timer, Bell, AlarmClock,
  Share2, Copy, MessageSquare, ExternalLink, Sparkles, Settings
} from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  getUserProfile,
  updateUserProfile,
  checkUsernameAvailability,
  getStudySessions,
  linkWithEmailPassword,
  linkWithGoogle,
  unlinkProvider,
  getUserProviders,
  getExtendedUserProfile
} from "@/utils/supabase";

// Types and interfaces
interface UserStats {
  totalStudyTime: number;
  studyStreak: number;
  totalSessions: number;
  averageSessionLength: number;
  subjectBreakdown: { [subject: string]: number };
}

// Timer Settings Interface
interface TimerSettings {
  workDuration: number; // seconds
  shortBreakDuration: number; // seconds
  longBreakDuration: number; // seconds
  sessionsUntilLongBreak: number;
  notificationInterval: number; // minutes
  dayResetHour: number; // Hour (0-23) when the day resets for logging
}

// Default timer settings
const DEFAULT_TIMER_SETTINGS: TimerSettings = {
  workDuration: 25 * 60,
  shortBreakDuration: 5 * 60,
  longBreakDuration: 15 * 60,
  sessionsUntilLongBreak: 4,
  notificationInterval: 60, // Default to 60 minutes
  dayResetHour: 4, // Default day reset time to 4 AM
};

// Add interface for productivity component visibility settings
interface ProductivityVisibilitySettings {
  showQuotes: boolean;
  showTasks: boolean;
  showExamCountdown: boolean;
  showSpotifyBar: boolean;
  spotifyCollapsed: boolean;
}

// Default visibility settings
const DEFAULT_VISIBILITY_SETTINGS: ProductivityVisibilitySettings = {
  showQuotes: true,
  showTasks: true,
  showExamCountdown: true,
  showSpotifyBar: true,
  spotifyCollapsed: false
};

const SettingsPage = () => {
  const { user } = useSupabaseAuth();
  const navigate = useNavigate();
  const [username, setUsername] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();
  const { theme, toggleTheme } = useTheme();
  const [stats, setStats] = useState<UserStats | null>(null);
  const [providers, setProviders] = useState<string[]>([]);
  const [linkingPassword, setLinkingPassword] = useState("");
  const [linkingLoading, setLinkingLoading] = useState(false);
  const [linkingError, setLinkingError] = useState("");
  const [showExistingEmailForm, setShowExistingEmailForm] = useState(false);
  const [existingEmail, setExistingEmail] = useState("");
  const [existingPassword, setExistingPassword] = useState("");


  // Productivity settings
  const [timerSettings, setTimerSettings] = useState<TimerSettings>(DEFAULT_TIMER_SETTINGS);

  // Replace individual Spotify settings with the visibility settings
  const [visibilitySettings, setVisibilitySettings] = useState<ProductivityVisibilitySettings>(DEFAULT_VISIBILITY_SETTINGS);

  // Notification state
  const [notificationPermission, setNotificationPermission] = useState<"default" | "granted" | "denied">("default");

  useEffect(() => {
    // Check for notifications support and set based on stored permission
    if ('Notification' in window) {
      setNotificationPermission(Notification.permission as "default" | "granted" | "denied");
    }

    const fetchUserProfile = async () => {
      if (!user) {
        navigate('/');
        return;
      }

      setIsLoading(true);
      try {
        // Get user profile from Supabase
        const userData = await getUserProfile(user.id);

        if (userData) {
          setUsername(userData.username || "");

          // Load productivity visibility settings from user document if available
          if (userData.progress?.productivitySettings) {
            setVisibilitySettings({
              showQuotes: userData.progress.productivitySettings.showQuotes ?? DEFAULT_VISIBILITY_SETTINGS.showQuotes,
              showTasks: userData.progress.productivitySettings.showTasks ?? DEFAULT_VISIBILITY_SETTINGS.showTasks,
              showExamCountdown: userData.progress.productivitySettings.showExamCountdown ?? DEFAULT_VISIBILITY_SETTINGS.showExamCountdown,
              showSpotifyBar: userData.progress.productivitySettings.showSpotifyBar ?? DEFAULT_VISIBILITY_SETTINGS.showSpotifyBar,
              spotifyCollapsed: userData.progress.productivitySettings.spotifyCollapsed ?? DEFAULT_VISIBILITY_SETTINGS.spotifyCollapsed
            });

            // Also update localStorage for immediate effect
            localStorage.setItem('spotify-is-visible', JSON.stringify(userData.progress.productivitySettings.showSpotifyBar ?? true));
            localStorage.setItem('spotify-is-collapsed', JSON.stringify(userData.progress.productivitySettings.spotifyCollapsed ?? false));
          }

          // Load timer settings from user document if available
          if (userData.stats?.timerSettings) {
            const userTimerSettings = {
              workDuration: userData.stats.timerSettings.workDuration ?? DEFAULT_TIMER_SETTINGS.workDuration,
              shortBreakDuration: userData.stats.timerSettings.shortBreakDuration ?? DEFAULT_TIMER_SETTINGS.shortBreakDuration,
              longBreakDuration: userData.stats.timerSettings.longBreakDuration ?? DEFAULT_TIMER_SETTINGS.longBreakDuration,
              sessionsUntilLongBreak: userData.stats.timerSettings.sessionsUntilLongBreak ?? DEFAULT_TIMER_SETTINGS.sessionsUntilLongBreak,
              notificationInterval: userData.stats.timerSettings.notificationInterval ?? DEFAULT_TIMER_SETTINGS.notificationInterval,
              dayResetHour: userData.stats.timerSettings.dayResetHour ?? DEFAULT_TIMER_SETTINGS.dayResetHour
            };

            setTimerSettings(userTimerSettings);

            // Also update localStorage to maintain compatibility with existing code
            localStorage.setItem('timerSettings', JSON.stringify(userTimerSettings));
          } else {
            // If no timer settings in Supabase, use localStorage if available
            const storedSettings = localStorage.getItem('timerSettings');
            if (storedSettings) {
              try {
                const parsedSettings = JSON.parse(storedSettings);
                setTimerSettings(parsedSettings);
              } catch (e) {
                console.error('Error parsing stored timer settings:', e);
                // Use defaults if parsing fails
                setTimerSettings(DEFAULT_TIMER_SETTINGS);
              }
            }
          }
        }

        // Fetch user stats - calculate from study sessions
        const studySessions = await getStudySessions(user.id);
        const userStats = calculateUserStats(studySessions);
        setStats(userStats);

        // Get user's auth providers from Supabase auth
        const providers = await getUserProviders();
        setProviders(Array.isArray(providers) ? providers : []);


      } catch (error) {
        console.error('Error fetching user profile:', error);
        toast({
          title: "Error",
          description: "Failed to load user profile",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserProfile();
  }, [user, navigate]);

  // --- Notification Permission Request ---
  const requestNotificationPermission = async () => {
    if (!('Notification' in window)) {
      toast({
        title: "Notifications Not Supported",
        description: "Your browser does not support desktop notifications.",
        variant: "destructive"
      });
      setNotificationPermission("denied");
      return;
    }

    // Return current status if already decided
    if (notificationPermission === 'granted' || notificationPermission === 'denied') {
      return;
    }

    // Request permission
    try {
      const permission = await Notification.requestPermission();
      setNotificationPermission(permission as "default" | "granted" | "denied"); // Update state
      if (permission === 'denied') {
        toast({
          title: "Notifications Disabled",
          description: "You have denied notification permissions. Please enable them in browser settings if you want timer alerts.",
          variant: "default",
          duration: 5000
        });
      } else if (permission === 'granted') {
        toast({
          title: "Notifications Enabled",
          description: "You will now receive timer alerts.",
          variant: "default"
        });
      }
    } catch (error) {
      console.error("Error requesting notification permission:", error);
      toast({
        title: "Permission Error",
        description: "Could not request notification permission.",
        variant: "destructive"
      });
      setNotificationPermission("denied"); // Assume denied on error
    }
  };

  // Handle timer settings update - save to Supabase
  const updateTimerSettings = async (newSettings: Partial<TimerSettings>) => {
    if (!user) return;

    // Update local state
    const updatedSettings = {
      ...timerSettings,
      ...newSettings
    };
    setTimerSettings(updatedSettings);

    // Update localStorage for immediate effect and compatibility
    localStorage.setItem('timerSettings', JSON.stringify(updatedSettings));

    try {
      // Get current user data to preserve other stats
      const userData = await getExtendedUserProfile(user.id);
      const currentStats = userData?.stats || {};

      // Save to Supabase in the correct format (stats.timerSettings)
      await updateUserProfile(user.id, {
        stats: {
          ...currentStats,
          timerSettings: updatedSettings
        }
      });

      toast({
        title: "Settings Updated",
        description: "Your timer settings have been saved.",
      });
    } catch (error) {
      console.error('Error saving timer settings:', error);
      toast({
        title: "Error",
        description: "Failed to save timer settings.",
        variant: "destructive",
      });
    }
  };

  // Update visibility settings and save to Supabase
  const updateVisibilitySettings = async (key: keyof ProductivityVisibilitySettings, value: boolean) => {
    if (!user) return;

    // Update local state
    const newSettings = { ...visibilitySettings, [key]: value };
    setVisibilitySettings(newSettings);

    // Update localStorage for immediate effect if it's a Spotify setting
    if (key === 'showSpotifyBar') {
      localStorage.setItem('spotify-is-visible', JSON.stringify(value));
    } else if (key === 'spotifyCollapsed') {
      localStorage.setItem('spotify-is-collapsed', JSON.stringify(value));
    }

    try {
      // Get current user data to preserve other progress data
      const userData = await getExtendedUserProfile(user.id);
      const currentProgress = userData?.progress || {};

      // Save to Supabase in the correct format (progress.productivitySettings)
      await updateUserProfile(user.id, {
        progress: {
          ...currentProgress,
          productivitySettings: newSettings
        }
      });

      toast({
        title: "Settings Updated",
        description: "Your productivity settings have been saved.",
      });
    } catch (error) {
      console.error('Error saving productivity settings:', error);
      toast({
        title: "Error",
        description: "Failed to save productivity settings.",
        variant: "destructive",
      });
    }
  };

  // Helper function to calculate user stats from study sessions
  const calculateUserStats = (studySessions: any[]): UserStats => {
    if (!studySessions || studySessions.length === 0) {
      return {
        totalStudyTime: 0,
        studyStreak: 0,
        totalSessions: 0,
        averageSessionLength: 0,
        subjectBreakdown: {}
      };
    }

    const totalStudyTime = studySessions.reduce((total, session) => total + (session.duration || 0), 0);
    const totalSessions = studySessions.length;
    const averageSessionLength = totalSessions > 0 ? totalStudyTime / totalSessions : 0;

    // Calculate subject breakdown
    const subjectBreakdown: { [subject: string]: number } = {};
    studySessions.forEach(session => {
      const subject = session.subject || 'Unknown';
      subjectBreakdown[subject] = (subjectBreakdown[subject] || 0) + (session.duration || 0);
    });

    // Calculate study streak (simplified)
    const dates = [...new Set(studySessions.map(s => s.date))].sort();
    let studyStreak = 0;
    if (dates.length > 0) {
      studyStreak = dates.length; // Simplified streak calculation
    }

    return {
      totalStudyTime,
      studyStreak,
      totalSessions,
      averageSessionLength,
      subjectBreakdown
    };
  };

  // Helper function to get auth providers - now uses the imported function
  const getUserAuthProviders = async (): Promise<string[]> => {
    return await getUserProviders();
  };

  const handleUsernameUpdate = async () => {
    if (!user || !username.trim() || username.trim().length < 3) return;

    setIsUpdating(true);
    try {
      // Check username availability before updating
      const isAvailable = await checkUsernameAvailability(username.trim());
      if (!isAvailable) {
        toast({
          title: "Error",
          description: "This username is already taken. Please choose a different one.",
          variant: "destructive",
        });
        return;
      }

      await updateUserProfile(user.id, {
        username: username.trim(),
        photo_url: user.user_metadata?.avatar_url || "",
        display_name: user.user_metadata?.full_name || username.trim(),
      });

      toast({
        title: "Profile Updated",
        description: "Your username has been successfully updated.",
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteChats = async () => {
    if (!user) return;

    try {
      // Clear localStorage
      localStorage.removeItem("chatHistoryList");
      localStorage.removeItem("currentChatId");

      toast({
        title: "Chats Deleted",
        description: "All chat history has been deleted successfully.",
      });
    } catch (error) {
      console.error('Error deleting all chats:', error);
      toast({
        title: "Error",
        description: "Failed to delete all chats. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleLinkWithEmail = async () => {
    if (!user) return;
    if (!linkingPassword || linkingPassword.length < 6) {
      setLinkingError("Password must be at least 6 characters");
      return;
    }

    setLinkingLoading(true);
    setLinkingError("");

    try {
      await linkWithEmailPassword(linkingPassword);
      const updatedProviders = await getUserProviders();
      setProviders(Array.isArray(updatedProviders) ? updatedProviders : []);
      toast({
        title: "Account Linked",
        description: "Your account has been linked with email and password",
      });
      setLinkingPassword("");
    } catch (error: any) {
      console.error('Error linking with email:', error);

      // Handle specific Supabase errors
      if (error.message?.includes('already registered')) {
        setShowExistingEmailForm(true);
        setLinkingError("This email is already registered. Please sign in with that account to link them.");
      } else {
        setLinkingError(error.message || "Failed to link account with email");
      }
    } finally {
      setLinkingLoading(false);
    }
  };

  const handleLinkWithGoogle = async () => {
    if (!user) return;

    setLinkingLoading(true);
    setLinkingError("");

    try {
      const result = await linkWithGoogle();

      // If there's a URL, the user will be redirected to Google OAuth
      if (result.url) {
        toast({
          title: "Redirecting to Google",
          description: "You will be redirected to Google to link your account.",
        });
        // The redirect happens automatically in the linkWithGoogle function
        return;
      }

      // If no redirect, update providers immediately
      const updatedProviders = await getUserProviders();
      setProviders(Array.isArray(updatedProviders) ? updatedProviders : []);
      toast({
        title: "Account Linked",
        description: "Your account has been linked with Google",
      });
    } catch (error: any) {
      console.error('Error linking with Google:', error);
      setLinkingError(error.message || "Failed to link account with Google");
    } finally {
      setLinkingLoading(false);
    }
  };

  const handleUnlinkProvider = async (providerId: string) => {
    if (!user) return;

    // Don't allow unlinking if there's only one provider
    if (!Array.isArray(providers) || providers.length <= 1) {
      toast({
        title: "Cannot Unlink",
        description: "You need at least one login method",
        variant: "destructive",
      });
      return;
    }

    setLinkingLoading(true);

    try {
      // TODO: Implement provider unlinking for Supabase
      // This feature is not currently implemented in Supabase auth
      toast({
        title: "Feature Not Available",
        description: "Account unlinking is not currently available. Please contact support.",
        variant: "destructive",
      });
    } catch (error: any) {
      console.error('Error unlinking provider:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to unlink provider",
        variant: "destructive",
      });
    } finally {
      setLinkingLoading(false);
    }
  };

  const handleCancelLinkExisting = () => {
    setShowExistingEmailForm(false);
    setExistingEmail("");
    setExistingPassword("");
    setLinkingError("");
  };

  if (!user) return null;

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-[#030303] via-[#0a0a0a] to-[#1a1a1a] relative overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      {/* Enhanced Background decorative elements */}
      <div className="absolute inset-0 -z-20">
        {/* Animated gradient orbs */}
        <motion.div
          className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-violet-500/20 via-purple-500/10 to-transparent rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
            x: [0, 50, 0],
            y: [0, -30, 0]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          style={{ transform: 'translate(30%, -30%)' }}
        />
        <motion.div
          className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-emerald-500/15 via-teal-500/10 to-transparent rounded-full blur-3xl"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.2, 0.4, 0.2],
            x: [0, -40, 0],
            y: [0, 40, 0]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          style={{ transform: 'translate(-25%, 25%)' }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-r from-rose-500/10 via-pink-500/5 to-transparent rounded-full blur-2xl"
          animate={{
            rotate: [0, 360],
            scale: [1, 1.3, 1],
            opacity: [0.1, 0.3, 0.1]
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transform: 'translate(-50%, -50%)' }}
        />
      </div>

      {/* Grid pattern overlay */}
      <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] -z-10" />

      {/* Floating particles */}
      <div className="absolute inset-0 -z-10">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-violet-400/30 rounded-full"
            animate={{
              y: [0, -100, 0],
              x: [0, Math.random() * 100 - 50, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "easeInOut"
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>

      <motion.div
        className="container max-w-5xl py-12 px-4 md:px-6 relative"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <motion.div
          className="flex items-center gap-3 mb-10 group"
          initial={{ x: -20, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate(-1)}
            className="rounded-full bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-violet-500/20 hover:text-violet-300 hover:border-violet-500/30 transition-all duration-300 shadow-lg hover:shadow-violet-500/25 hover:scale-105"
          >
            <ArrowLeft className="h-5 w-5 group-hover:-translate-x-0.5 transition-transform text-white/80" />
          </Button>
          <div>
            <motion.h1
              className="text-3xl font-bold tracking-tight bg-gradient-to-r from-white via-violet-200 to-violet-400 bg-clip-text text-transparent"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <Settings className="inline-block h-8 w-8 mr-3 text-violet-400" />
              Settings
            </motion.h1>
            <motion.div
              className="h-1 w-16 bg-gradient-to-r from-violet-500 via-purple-500 to-pink-500 rounded-full mt-2 shadow-lg shadow-violet-500/50"
              initial={{ width: 0 }}
              animate={{ width: 64 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <Tabs defaultValue="profile" className="w-full">
            <div className="flex flex-col md:flex-row gap-8">
              <motion.div
                className="w-full md:w-64 shrink-0 flex flex-col space-y-6"
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <TabsList className="flex flex-row md:flex-col h-auto bg-black/20 backdrop-blur-md p-1 md:p-3 space-y-0 md:space-y-2 rounded-xl md:rounded-2xl border border-white/10 shadow-2xl shadow-black/50 overflow-x-auto md:overflow-visible">
                <TabsTrigger
                  value="profile"
                  className="w-full justify-start data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:border-primary/30 rounded-none md:rounded-lg py-3 px-4 transition-all duration-200 hover:bg-muted/80 group border border-transparent"
                >
                  <div className="bg-background/80 p-1.5 rounded-md mr-3 shadow-sm group-data-[state=active]:bg-primary/20 transition-colors">
                    <User className="h-4 w-4 group-data-[state=active]:text-primary" />
                  </div>
                  <span>Profile</span>
                </TabsTrigger>
                <TabsTrigger
                  value="account"
                  className="w-full justify-start data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:border-primary/30 rounded-none md:rounded-lg py-3 px-4 transition-all duration-200 hover:bg-muted/80 group border border-transparent"
                >
                  <div className="bg-background/80 p-1.5 rounded-md mr-3 shadow-sm group-data-[state=active]:bg-primary/20 transition-colors">
                    <Shield className="h-4 w-4 group-data-[state=active]:text-primary" />
                  </div>
                  <span>Account</span>
                </TabsTrigger>
                <TabsTrigger
                  value="productivity"
                  className="w-full justify-start data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:border-primary/30 rounded-none md:rounded-lg py-3 px-4 transition-all duration-200 hover:bg-muted/80 group border border-transparent"
                >
                  <div className="bg-background/80 p-1.5 rounded-md mr-3 shadow-sm group-data-[state=active]:bg-primary/20 transition-colors">
                    <Timer className="h-4 w-4 group-data-[state=active]:text-primary" />
                  </div>
                  <span>Productivity</span>
                </TabsTrigger>
                <TabsTrigger
                  value="appearance"
                  className="w-full justify-start data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:border-primary/30 rounded-none md:rounded-lg py-3 px-4 transition-all duration-200 hover:bg-muted/80 group border border-transparent"
                >
                  <div className="bg-background/80 p-1.5 rounded-md mr-3 shadow-sm group-data-[state=active]:bg-primary/20 transition-colors">
                    <Palette className="h-4 w-4 group-data-[state=active]:text-primary" />
                  </div>
                  <span>Appearance</span>
                </TabsTrigger>
                <TabsTrigger
                  value="data"
                  className="w-full justify-start data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:border-primary/30 rounded-none md:rounded-lg py-3 px-4 transition-all duration-200 hover:bg-muted/80 group border border-transparent"
                >
                  <div className="bg-background/80 p-1.5 rounded-md mr-3 shadow-sm group-data-[state=active]:bg-primary/20 transition-colors">
                    <Activity className="h-4 w-4 group-data-[state=active]:text-primary" />
                  </div>
                  <span>Data</span>
                </TabsTrigger>

              </TabsList>

              {/* Share Section */}
              <Card className="overflow-hidden border-0 shadow-xl hidden md:block bg-gradient-to-br from-background via-muted/30 to-primary/5 backdrop-blur-sm rounded-xl relative">
                {/* Decorative elements */}
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-primary/10 to-transparent rounded-full blur-xl -z-10 transform translate-x-1/3 -translate-y-1/3"></div>
                <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-tr from-primary/10 to-transparent rounded-full blur-xl -z-10 transform -translate-x-1/3 translate-y-1/3"></div>

                <CardHeader className="p-4 pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="bg-primary/20 p-2 rounded-full shadow-sm">
                        <Share2 className="h-4 w-4 text-primary animate-pulse" />
                      </div>
                      <div>
                        <CardTitle className="text-base bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">Share IsotopeAI</CardTitle>
                        <div className="h-0.5 w-8 bg-gradient-to-r from-primary to-primary/20 rounded-full mt-0.5"></div>
                      </div>
                    </div>
                  </div>
                  <CardDescription className="text-xs mt-2 italic">
                    Help your friends excel in their studies!
                  </CardDescription>
                </CardHeader>

                <CardContent className="p-4 pt-2 space-y-3">
                  <Button
                    size="sm"
                    className="w-full bg-gradient-to-r from-[#25D366] to-[#128C7E] hover:opacity-90 hover:shadow-md text-white gap-1.5 h-9 rounded-lg shadow-sm transition-all duration-200 hover:translate-y-[-1px]"
                    onClick={() => {
                      const encodedMessage = encodeURIComponent(`Hey!\n\nCheck out *IsotopeAI* - a FREE productivity platform for students:\n\n• AI-powered study assistant\n• Pomodoro timer & study tracker\n• Daily analytics & progress charts\n• Task management system\n• Groups to study together\n• Mock Tests Analysis\n\nIt's helping me stay organized and productive.\n\nhttps://isotopeai.in`);
                      window.open(`https://wa.me/?text=${encodedMessage}`, '_blank');
                    }}
                  >
                    <svg
                      className="h-4 w-4"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M17.415 14.382c-.298-.149-1.759-.867-2.031-.967-.272-.099-.47-.148-.669.15-.198.296-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.019-.458.13-.606.134-.133.297-.347.446-.52.149-.174.198-.298.297-.497.1-.198.05-.371-.025-.52-.074-.149-.668-1.612-.916-2.207-.241-.579-.486-.5-.668-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.064 2.875 1.213 ************* 2.095 3.2 5.076 4.487.71.306 1.263.489 1.694.625.712.227 1.36.195 1.871.118.57-.085 1.758-.719 2.006-1.413.247-.694.247-1.289.173-1.413-.074-.124-.272-.198-.57-.347z"
                      />
                      <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 100-16 8 8 0 000 16z" />
                    </svg>
                    Share on WhatsApp
                  </Button>

                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      size="sm"
                      className="gap-1.5 h-9 rounded-lg shadow-sm bg-gradient-to-r from-[#833AB4] via-[#FD1D1D] to-[#FCAF45] hover:opacity-90 hover:shadow-md text-white transition-all duration-200 hover:translate-y-[-1px]"
                      onClick={() => {
                        window.open('https://www.instagram.com/isotope.ai/', '_blank');
                      }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect>
                        <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                        <line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line>
                      </svg>
                      Instagram
                    </Button>

                    <Button
                      size="sm"
                      className="gap-1.5 h-9 rounded-lg shadow-sm bg-gradient-to-r from-[#FF4500] to-[#FF5722] hover:opacity-90 hover:shadow-md text-white transition-all duration-200 hover:translate-y-[-1px]"
                      onClick={() => {
                        window.open('https://www.reddit.com/r/Isotope/', '_blank');
                      }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <circle cx="12" cy="12" r="10" />
                        <circle cx="12" cy="9" r="1" />
                        <circle cx="12" cy="15" r="1" />
                        <path d="M8.5 9a2 2 0 0 0-2 2v0c0 1.1.9 2 2 2" />
                        <path d="M15.5 9a2 2 0 0 1 2 2v0c0 1.1-.9 2-2 2" />
                        <path d="M7.5 13h9" />
                        <path d="M10 16v-3" />
                        <path d="M14 16v-3" />
                      </svg>
                      Reddit
                    </Button>
                  </div>

                  <div className="relative mt-2 pt-3 border-t border-border/30">
                    <Button
                      size="sm"
                      variant="ghost"
                      className="w-full gap-1.5 h-8 text-xs rounded-lg group hover:bg-primary/5"
                      onClick={() => {
                        navigator.clipboard.writeText('https://isotopeai.in');
                        toast({
                          title: "Copied to clipboard",
                          description: "Website URL copied to clipboard",
                        });
                      }}
                    >
                      <Copy className="h-3.5 w-3.5 group-hover:text-primary transition-colors" />
                      <span className="group-hover:text-primary transition-colors">Copy Website Link</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="flex-1">
              <TabsContent value="profile" className="m-0 mt-6 md:mt-0">
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-semibold tracking-tight mb-1 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">Profile</h2>
                    <div className="h-1 w-12 bg-gradient-to-r from-primary to-primary/30 rounded-full mb-3"></div>
                    <p className="text-muted-foreground">
                      Manage your personal information and profile settings.
                    </p>
                  </div>

                  <div className="flex flex-col sm:flex-row items-center gap-6 p-6 bg-gradient-to-br from-background to-muted/30 rounded-xl border border-border/30 shadow-sm backdrop-blur-sm relative overflow-hidden">
                    {/* Decorative element */}
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/5 to-transparent rounded-full blur-xl -z-10 transform translate-x-1/3 -translate-y-1/3"></div>

                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-md"></div>
                      <Avatar className="h-24 w-24 border-4 border-background shadow-xl relative">
                        <AvatarImage src={user.user_metadata?.avatar_url || undefined} alt={user.user_metadata?.full_name || "User"} />
                        <AvatarFallback className="text-2xl bg-gradient-to-br from-primary/30 to-primary/10">
                          {user.user_metadata?.full_name?.[0]?.toUpperCase() || user.email?.[0]?.toUpperCase() || "U"}
                        </AvatarFallback>
                      </Avatar>
                    </div>

                    <div className="flex-1 text-center sm:text-left mt-4 sm:mt-0">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div>
                          <h3 className="text-xl font-medium">{user.user_metadata?.full_name || username || "User"}</h3>
                          <p className="text-sm text-muted-foreground flex items-center justify-center sm:justify-start gap-1.5 mt-1">
                            <Mail className="h-3.5 w-3.5 text-muted-foreground/70" />
                            {user.email}
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => navigate('/profile')}
                          className="group hover:bg-primary/10 hover:text-primary hover:border-primary/30 transition-all duration-200"
                        >
                          View Profile
                          <ChevronRight className="ml-2 h-4 w-4 group-hover:translate-x-0.5 transition-transform" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="p-6 rounded-xl border border-border/40 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm shadow-sm">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="bg-primary/10 p-1.5 rounded-md">
                        <User className="h-4 w-4 text-primary" />
                      </div>
                      <h3 className="text-base font-medium">Username</h3>
                    </div>

                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Your username will be displayed across the platform and helps others identify you.
                      </p>
                      <div className="flex flex-col sm:flex-row gap-3">
                        <Input
                          id="username"
                          placeholder="Enter username (min. 3 characters)"
                          value={username}
                          onChange={(e) => setUsername(e.target.value)}
                          disabled={isLoading || isUpdating}
                          className="flex-1 bg-background/80 border-border/50 focus-visible:ring-primary/30 transition-all duration-200"
                        />
                        <Button
                          onClick={handleUsernameUpdate}
                          disabled={isLoading || isUpdating || username.trim().length < 3}
                          className="sm:w-auto w-full bg-primary/90 hover:bg-primary transition-all duration-200 shadow-sm"
                        >
                          {isUpdating ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                          Save Username
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="account" className="m-0 mt-6 md:mt-0">
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-semibold tracking-tight mb-1 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">Account</h2>
                    <div className="h-1 w-12 bg-gradient-to-r from-primary to-primary/30 rounded-full mb-3"></div>
                    <p className="text-muted-foreground">
                      Manage your login methods and account preferences.
                    </p>
                  </div>

                  <div className="space-y-6 rounded-xl border border-border/40 p-6 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm shadow-sm relative overflow-hidden">
                    {/* Decorative element */}
                    <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-primary/5 to-transparent rounded-full blur-xl -z-10 transform -translate-x-1/3 translate-y-1/3"></div>

                    <div>
                      <div className="flex items-center gap-2 mb-4">
                        <div className="bg-primary/10 p-1.5 rounded-md">
                          <Shield className="h-4 w-4 text-primary" />
                        </div>
                        <h3 className="text-base font-medium">Current Login Methods</h3>
                      </div>

                      <div className="space-y-3">
                        {Array.isArray(providers) && providers.includes('password') && (
                          <div className="flex items-center justify-between py-3 px-4 rounded-lg transition-colors bg-background/60 border border-border/30 hover:bg-background/80 hover:border-border/50 shadow-sm">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center justify-center h-10 w-10 rounded-full bg-primary/10 text-primary shadow-sm">
                                <Mail className="h-4 w-4" />
                              </div>
                              <div>
                                <p className="text-sm font-medium">Email and Password</p>
                                <p className="text-xs text-muted-foreground">{user.email}</p>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleUnlinkProvider('password')}
                              disabled={linkingLoading || !Array.isArray(providers) || providers.length <= 1}
                              className="h-8 w-8 rounded-full text-muted-foreground hover:text-destructive hover:bg-destructive/10 transition-colors"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        )}

                        {Array.isArray(providers) && providers.includes('google.com') && (
                          <div className="flex items-center justify-between py-3 px-4 rounded-lg transition-colors bg-background/60 border border-border/30 hover:bg-background/80 hover:border-border/50 shadow-sm">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center justify-center h-10 w-10 rounded-full bg-primary/10 text-primary shadow-sm">
                                <svg viewBox="0 0 24 24" width="16" height="16">
                                  <g transform="matrix(1, 0, 0, 1, 27.009001, -39.238998)">
                                    <path fill="#4285F4" d="M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z" />
                                    <path fill="#34A853" d="M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z" />
                                    <path fill="#FBBC05" d="M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z" />
                                    <path fill="#EA4335" d="M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z" />
                                  </g>
                                </svg>
                              </div>
                              <div>
                                <p className="text-sm font-medium">Google</p>
                                <p className="text-xs text-muted-foreground">{user.email}</p>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleUnlinkProvider('google.com')}
                              disabled={linkingLoading || !Array.isArray(providers) || providers.length <= 1}
                              className="h-8 w-8 rounded-full text-muted-foreground hover:text-destructive hover:bg-destructive/10 transition-colors"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        )}

                        {Array.isArray(providers) && providers.length === 0 && (
                          <Alert variant="destructive" className="border border-destructive/20 bg-destructive/5">
                            <AlertTitle className="font-medium">No login methods found</AlertTitle>
                            <AlertDescription className="text-sm">
                              This is unusual. Please contact support.
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    </div>

                    <Separator className="bg-border/50" />

                    {/* Link with Email */}
                    {Array.isArray(providers) && !providers.includes('password') && !showExistingEmailForm && (
                      <div className="space-y-3">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="bg-primary/10 p-1.5 rounded-md">
                            <Mail className="h-4 w-4 text-primary" />
                          </div>
                          <h3 className="text-base font-medium">Link with Email & Password</h3>
                        </div>

                        <div className="space-y-3 max-w-md bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm">
                          <div className="space-y-2">
                            <Label htmlFor="linking-password" className="text-sm font-medium">Set Password</Label>
                            <Input
                              id="linking-password"
                              type="password"
                              placeholder="Create a password (min. 6 characters)"
                              value={linkingPassword}
                              onChange={(e) => setLinkingPassword(e.target.value)}
                              disabled={linkingLoading}
                              className="bg-background/80 border-border/50 focus-visible:ring-primary/30 transition-all duration-200"
                            />
                          </div>
                          {linkingError && !showExistingEmailForm && (
                            <p className="text-sm text-destructive bg-destructive/5 p-2 rounded-md border border-destructive/20">{linkingError}</p>
                          )}
                          <Button
                            onClick={handleLinkWithEmail}
                            disabled={linkingLoading || !linkingPassword || linkingPassword.length < 6}
                            className="w-full bg-primary/90 hover:bg-primary transition-all duration-200 shadow-sm text-primary-foreground"
                          >
                            {linkingLoading ? (
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                              <Mail className="mr-2 h-4 w-4" />
                            )}
                            Link with Email
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Form for linking with existing email account */}
                    {Array.isArray(providers) && !providers.includes('password') && showExistingEmailForm && (
                      <div className="space-y-3">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="bg-primary/10 p-1.5 rounded-md">
                            <LinkIcon className="h-4 w-4 text-primary" />
                          </div>
                          <h3 className="text-base font-medium">Link with Existing Email Account</h3>
                        </div>

                        <Alert className="max-w-md border border-amber-200/30 bg-amber-50/50 dark:bg-amber-900/10 dark:border-amber-400/20 text-amber-800 dark:text-amber-300">
                          <AlertTitle className="font-medium">This email is already registered</AlertTitle>
                          <AlertDescription className="text-sm">
                            Please sign in with your existing email account to merge it with your Google account.
                          </AlertDescription>
                        </Alert>

                        <div className="space-y-3 max-w-md bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm">
                          <div className="space-y-2">
                            <Label htmlFor="existing-email" className="text-sm font-medium">Email</Label>
                            <Input
                              id="existing-email"
                              type="email"
                              placeholder="Enter your existing email"
                              value={existingEmail}
                              onChange={(e) => setExistingEmail(e.target.value)}
                              disabled={linkingLoading}
                              className="bg-background/80 border-border/50 focus-visible:ring-primary/30 transition-all duration-200"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="existing-password" className="text-sm font-medium">Password</Label>
                            <Input
                              id="existing-password"
                              type="password"
                              placeholder="Enter your existing password"
                              value={existingPassword}
                              onChange={(e) => setExistingPassword(e.target.value)}
                              disabled={linkingLoading}
                              className="bg-background/80 border-border/50 focus-visible:ring-primary/30 transition-all duration-200"
                            />
                          </div>
                          {linkingError && showExistingEmailForm && (
                            <p className="text-sm text-destructive bg-destructive/5 p-2 rounded-md border border-destructive/20">{linkingError}</p>
                          )}
                          <div className="flex gap-2">
                            <Button
                              onClick={() => {}}
                              disabled={linkingLoading || !existingEmail || !existingPassword}
                              className="flex-1 bg-primary/90 hover:bg-primary transition-all duration-200 shadow-sm text-primary-foreground"
                            >
                              {linkingLoading ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              ) : (
                                <LinkIcon className="mr-2 h-4 w-4" />
                              )}
                              Link Accounts
                            </Button>
                            <Button
                              onClick={handleCancelLinkExisting}
                              variant="outline"
                              disabled={linkingLoading}
                              className="hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30 transition-all duration-200"
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Link with Google */}
                    {Array.isArray(providers) && !providers.includes('google.com') && (
                      <div className="space-y-3">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="bg-primary/10 p-1.5 rounded-md">
                            <svg className="h-4 w-4 text-primary" viewBox="0 0 24 24">
                              <g transform="matrix(1, 0, 0, 1, 27.009001, -39.238998)">
                                <path fill="#4285F4" d="M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z" />
                                <path fill="#34A853" d="M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z" />
                                <path fill="#FBBC05" d="M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z" />
                                <path fill="#EA4335" d="M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z" />
                              </g>
                            </svg>
                          </div>
                          <h3 className="text-base font-medium">Link with Google</h3>
                        </div>

                        <div className="max-w-md bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm">
                          <Button
                            onClick={handleLinkWithGoogle}
                            disabled={linkingLoading}
                            className="w-full bg-white hover:bg-gray-50 text-gray-800 border border-gray-300 shadow-sm transition-all duration-200 hover:shadow group"
                          >
                            {linkingLoading ? (
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                              <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                                <g transform="matrix(1, 0, 0, 1, 27.009001, -39.238998)">
                                  <path fill="#4285F4" d="M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z" />
                                  <path fill="#34A853" d="M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z" />
                                  <path fill="#FBBC05" d="M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z" />
                                  <path fill="#EA4335" d="M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z" />
                                </g>
                              </svg>
                            )}
                            <span className="group-hover:translate-x-0.5 transition-transform">Link with Google</span>
                          </Button>
                          <p className="text-xs text-muted-foreground mt-2 text-center">
                            Connect your Google account for easier sign-in
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="productivity" className="m-0 mt-6 md:mt-0">
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-semibold tracking-tight mb-1 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">Productivity Settings</h2>
                    <div className="h-1 w-12 bg-gradient-to-r from-primary to-primary/30 rounded-full mb-3"></div>
                    <p className="text-muted-foreground">
                      Customize your study timer and productivity features.
                    </p>
                  </div>

                  {/* Pomodoro Timer Settings */}
                  <div className="space-y-6 rounded-xl border border-border/40 p-6 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm shadow-sm relative overflow-hidden">
                    {/* Decorative element */}
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/5 to-transparent rounded-full blur-xl -z-10 transform translate-x-1/3 -translate-y-1/3"></div>

                    <div>
                      <div className="flex items-center gap-2 mb-3">
                        <div className="bg-primary/10 p-1.5 rounded-md">
                          <Timer className="h-4 w-4 text-primary" />
                        </div>
                        <h3 className="text-base font-medium">Pomodoro Timer</h3>
                      </div>

                      <p className="text-sm text-muted-foreground mb-5 ml-8">
                        Configure the duration of work and break periods for the Pomodoro technique.
                      </p>

                      <div className="grid gap-5 md:grid-cols-2">
                        <div className="bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm space-y-2 transition-all duration-200 hover:shadow-md hover:border-border/50">
                          <Label htmlFor="workDuration" className="text-sm font-medium flex items-center gap-1.5">
                            <AlarmClock className="h-3.5 w-3.5 text-primary/70" />
                            Work Duration (minutes)
                          </Label>
                          <Input
                            id="workDuration"
                            type="number"
                            min="1"
                            value={Math.floor(timerSettings.workDuration / 60)}
                            onChange={(e) => {
                              const minutes = parseInt(e.target.value);
                              if (!isNaN(minutes) && minutes > 0) {
                                updateTimerSettings({ workDuration: minutes * 60 });
                              }
                            }}
                            className="bg-background/80 border-border/50 focus-visible:ring-primary/30 transition-all duration-200"
                          />
                        </div>

                        <div className="bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm space-y-2 transition-all duration-200 hover:shadow-md hover:border-border/50">
                          <Label htmlFor="shortBreak" className="text-sm font-medium flex items-center gap-1.5">
                            <AlarmClock className="h-3.5 w-3.5 text-primary/70" />
                            Short Break (minutes)
                          </Label>
                          <Input
                            id="shortBreak"
                            type="number"
                            min="1"
                            value={Math.floor(timerSettings.shortBreakDuration / 60)}
                            onChange={(e) => {
                              const minutes = parseInt(e.target.value);
                              if (!isNaN(minutes) && minutes > 0) {
                                updateTimerSettings({ shortBreakDuration: minutes * 60 });
                              }
                            }}
                            className="bg-background/80 border-border/50 focus-visible:ring-primary/30 transition-all duration-200"
                          />
                        </div>

                        <div className="bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm space-y-2 transition-all duration-200 hover:shadow-md hover:border-border/50">
                          <Label htmlFor="longBreak" className="text-sm font-medium flex items-center gap-1.5">
                            <AlarmClock className="h-3.5 w-3.5 text-primary/70" />
                            Long Break (minutes)
                          </Label>
                          <Input
                            id="longBreak"
                            type="number"
                            min="1"
                            value={Math.floor(timerSettings.longBreakDuration / 60)}
                            onChange={(e) => {
                              const minutes = parseInt(e.target.value);
                              if (!isNaN(minutes) && minutes > 0) {
                                updateTimerSettings({ longBreakDuration: minutes * 60 });
                              }
                            }}
                            className="bg-background/80 border-border/50 focus-visible:ring-primary/30 transition-all duration-200"
                          />
                        </div>

                        <div className="bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm space-y-2 transition-all duration-200 hover:shadow-md hover:border-border/50">
                          <Label htmlFor="sessionsUntilLongBreak" className="text-sm font-medium flex items-center gap-1.5">
                            <AlarmClock className="h-3.5 w-3.5 text-primary/70" />
                            Sessions until Long Break
                          </Label>
                          <Input
                            id="sessionsUntilLongBreak"
                            type="number"
                            min="1"
                            value={timerSettings.sessionsUntilLongBreak}
                            onChange={(e) => {
                              const sessions = parseInt(e.target.value);
                              if (!isNaN(sessions) && sessions > 0) {
                                updateTimerSettings({ sessionsUntilLongBreak: sessions });
                              }
                            }}
                            className="bg-background/80 border-border/50 focus-visible:ring-primary/30 transition-all duration-200"
                          />
                        </div>
                      </div>
                    </div>

                    <Separator className="bg-border/50 my-8" />

                    {/* Notification settings */}
                    <div>
                      <div className="flex items-center gap-2 mb-3">
                        <div className="bg-primary/10 p-1.5 rounded-md">
                          <Bell className="h-4 w-4 text-primary" />
                        </div>
                        <h3 className="text-base font-medium">Notifications</h3>
                      </div>

                      <div className="space-y-5 ml-8">
                        <div className="grid gap-5 md:grid-cols-2">
                          <div className="bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm space-y-2 transition-all duration-200 hover:shadow-md hover:border-border/50">
                            <Label htmlFor="notificationInterval" className="text-sm font-medium flex items-center gap-1.5">
                              <Bell className="h-3.5 w-3.5 text-primary/70" />
                              Notification Interval (minutes)
                            </Label>
                            <Input
                              id="notificationInterval"
                              type="number"
                              min="1"
                              value={timerSettings.notificationInterval}
                              onChange={(e) => {
                                const interval = parseInt(e.target.value);
                                if (!isNaN(interval) && interval > 0) {
                                  updateTimerSettings({ notificationInterval: interval });
                                }
                              }}
                              className="bg-background/80 border-border/50 focus-visible:ring-primary/30 transition-all duration-200"
                            />
                            <p className="text-xs text-muted-foreground mt-1">
                              Get notified after each interval while using the stopwatch mode
                            </p>
                          </div>

                          <div className="bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm space-y-2 transition-all duration-200 hover:shadow-md hover:border-border/50">
                            <Label htmlFor="dayResetHour" className="text-sm font-medium flex items-center gap-1.5">
                              <AlarmClock className="h-3.5 w-3.5 text-primary/70" />
                              Day Reset Hour (0-23)
                            </Label>
                            <Input
                              id="dayResetHour"
                              type="number"
                              min="0"
                              max="23"
                              value={timerSettings.dayResetHour}
                              onChange={(e) => {
                                const hour = parseInt(e.target.value);
                                if (!isNaN(hour) && hour >= 0 && hour <= 23) {
                                  updateTimerSettings({ dayResetHour: hour });
                                }
                              }}
                              className="bg-background/80 border-border/50 focus-visible:ring-primary/30 transition-all duration-200"
                            />
                            <p className="text-xs text-muted-foreground mt-1">
                              Hour when the day resets for logging purposes (e.g., 4 means 4 AM)
                            </p>
                          </div>
                        </div>

                        {('Notification' in window) && (
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-gradient-to-r from-background to-muted/30 p-5 rounded-lg border border-border/40 shadow-sm mt-2 relative overflow-hidden">
                            {/* Decorative element */}
                            <div className="absolute bottom-0 right-0 w-24 h-24 bg-gradient-to-tl from-primary/5 to-transparent rounded-full blur-xl -z-10 transform translate-x-1/3 translate-y-1/3"></div>

                            <div>
                              <h4 className="text-sm font-medium flex items-center gap-2">
                                <Bell className={`h-4 w-4 ${notificationPermission === 'granted' ? "text-green-500" : "text-muted-foreground"}`} />
                                Browser Notifications
                              </h4>
                              <p className="text-xs text-muted-foreground mt-1">
                                {notificationPermission === 'granted'
                                  ? "Notifications are enabled for timer alerts and reminders"
                                  : notificationPermission === 'denied'
                                  ? "Notifications are blocked in your browser settings"
                                  : "Allow notifications for timer alerts and reminders"}
                              </p>
                            </div>
                            <Button
                              onClick={requestNotificationPermission}
                              variant="outline"
                              size="sm"
                              disabled={notificationPermission === 'denied'}
                              className={notificationPermission === 'granted'
                                ? "bg-green-500/10 text-green-500 hover:bg-green-500/20 hover:text-green-600 border-green-500/20 transition-all duration-200"
                                : "transition-all duration-200 hover:border-primary/30 hover:bg-primary/10 hover:text-primary"}
                            >
                              {notificationPermission === 'granted' ? (
                                <>
                                  <Bell className="h-4 w-4 mr-2" />
                                  Enabled
                                </>
                              ) : (
                                <>
                                  {notificationPermission === 'denied' ? "Blocked by Browser" : "Enable Notifications"}
                                </>
                              )}
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>

                    <Separator className="bg-border/50 my-8" />

                    {/* Feature toggles - Update this section */}
                    <div>
                      <div className="flex items-center gap-2 mb-3">
                        <div className="bg-primary/10 p-1.5 rounded-md">
                          <Activity className="h-4 w-4 text-primary" />
                        </div>
                        <h3 className="text-base font-medium">Productivity Page Components</h3>
                      </div>

                      <p className="text-sm text-muted-foreground mb-5 ml-8">
                        Customize which components appear on your productivity page.
                      </p>

                      <div className="grid gap-4 md:grid-cols-2 ml-8">
                        {/* Motivational Quotes toggle */}
                        <div className="bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm transition-all duration-200 hover:shadow-md hover:border-border/50 flex flex-col justify-between h-full">
                          <div className="mb-4">
                            <Label htmlFor="quotesToggle" className="text-sm font-medium flex items-center gap-1.5">
                              <svg className="h-3.5 w-3.5 text-primary/70" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z" />
                                <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z" />
                              </svg>
                              Motivational Quotes
                            </Label>
                            <p className="text-xs text-muted-foreground mt-1">
                              Show inspirational study quotes on the productivity page
                            </p>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-medium text-muted-foreground">
                              {visibilitySettings.showQuotes ? "Visible" : "Hidden"}
                            </span>
                            <Switch
                              id="quotesToggle"
                              checked={visibilitySettings.showQuotes}
                              onCheckedChange={(checked) => updateVisibilitySettings('showQuotes', checked)}
                              disabled={isUpdating}
                              className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                            />
                          </div>
                        </div>

                        {/* Tasks toggle */}
                        <div className="bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm transition-all duration-200 hover:shadow-md hover:border-border/50 flex flex-col justify-between h-full">
                          <div className="mb-4">
                            <Label htmlFor="tasksToggle" className="text-sm font-medium flex items-center gap-1.5">
                              <svg className="h-3.5 w-3.5 text-primary/70" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <rect width="8" height="8" x="3" y="3" rx="1" />
                                <path d="m7 7 3 3" />
                                <path d="M7 10 10 7" />
                                <rect width="8" height="8" x="13" y="3" rx="1" />
                                <path d="m17 7 3 3" />
                                <path d="M17 10 20 7" />
                                <rect width="8" height="8" x="3" y="13" rx="1" />
                                <path d="m7 17 3 3" />
                                <path d="M7 20 10 17" />
                                <rect width="8" height="8" x="13" y="13" rx="1" />
                                <path d="m17 17 3 3" />
                                <path d="M17 20 20 17" />
                              </svg>
                              Tasks Menu
                            </Label>
                            <p className="text-xs text-muted-foreground mt-1">
                              Show the tasks dropdown button for managing to-do items
                            </p>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-medium text-muted-foreground">
                              {visibilitySettings.showTasks ? "Visible" : "Hidden"}
                            </span>
                            <Switch
                              id="tasksToggle"
                              checked={visibilitySettings.showTasks}
                              onCheckedChange={(checked) => updateVisibilitySettings('showTasks', checked)}
                              disabled={isUpdating}
                              className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                            />
                          </div>
                        </div>

                        {/* Exam Countdown toggle */}
                        <div className="bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm transition-all duration-200 hover:shadow-md hover:border-border/50 flex flex-col justify-between h-full">
                          <div className="mb-4">
                            <Label htmlFor="examCountdownToggle" className="text-sm font-medium flex items-center gap-1.5">
                              <AlarmClock className="h-3.5 w-3.5 text-primary/70" />
                              Exam Countdown
                            </Label>
                            <p className="text-xs text-muted-foreground mt-1">
                              Show countdown timer for upcoming exams
                            </p>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-medium text-muted-foreground">
                              {visibilitySettings.showExamCountdown ? "Visible" : "Hidden"}
                            </span>
                            <Switch
                              id="examCountdownToggle"
                              checked={visibilitySettings.showExamCountdown}
                              onCheckedChange={(checked) => updateVisibilitySettings('showExamCountdown', checked)}
                              disabled={isUpdating}
                              className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                            />
                          </div>
                        </div>

                        {/* Spotify toggle */}
                        <div className="bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm transition-all duration-200 hover:shadow-md hover:border-border/50 flex flex-col justify-between h-full">
                          <div className="mb-4">
                            <Label htmlFor="spotifyToggle" className="text-sm font-medium flex items-center gap-1.5">
                              <svg className="h-3.5 w-3.5 text-primary/70" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <circle cx="12" cy="12" r="10" />
                                <path d="M8 12.5a5 5 0 0 1 8 0" />
                                <path d="M9.5 10a3 3 0 0 1 5 0" />
                                <path d="M11 8a1 1 0 0 1 2 0" />
                              </svg>
                              Spotify Music Player
                            </Label>
                            <p className="text-xs text-muted-foreground mt-1">
                              Show Spotify playlist player on the productivity page
                            </p>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-medium text-muted-foreground">
                              {visibilitySettings.showSpotifyBar ? "Visible" : "Hidden"}
                            </span>
                            <Switch
                              id="spotifyToggle"
                              checked={visibilitySettings.showSpotifyBar}
                              onCheckedChange={(checked) => updateVisibilitySettings('showSpotifyBar', checked)}
                              disabled={isUpdating}
                              className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Spotify collapsed state toggle */}
                      {visibilitySettings.showSpotifyBar && (
                        <div className="ml-12 mt-3 bg-background/60 p-4 rounded-lg border border-border/30 shadow-sm transition-all duration-200 hover:shadow-md hover:border-border/50 border-l-primary/30 border-l-2">
                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="spotifyCollapsedToggle" className="text-sm font-medium flex items-center gap-1.5">
                                <svg className="h-3.5 w-3.5 text-primary/70" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="M19 5H5a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2Z" />
                                  <path d="M12 17v-6" />
                                  <path d="m9 11 3-3 3 3" />
                                </svg>
                                Collapsed Spotify Player
                              </Label>
                              <p className="text-xs text-muted-foreground mt-1">
                                Start with the Spotify player in collapsed mode
                              </p>
                            </div>
                            <Switch
                              id="spotifyCollapsedToggle"
                              checked={visibilitySettings.spotifyCollapsed}
                              onCheckedChange={(checked) => updateVisibilitySettings('spotifyCollapsed', checked)}
                              disabled={isUpdating}
                              className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="appearance" className="m-0 mt-6 md:mt-0">
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-semibold tracking-tight mb-1 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">Appearance</h2>
                    <div className="h-1 w-12 bg-gradient-to-r from-primary to-primary/30 rounded-full mb-3"></div>
                    <p className="text-muted-foreground">
                      Customize how the application looks and feels.
                    </p>
                  </div>

                  <div className="rounded-xl border border-border/40 p-6 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm shadow-sm relative overflow-hidden">
                    {/* Decorative elements */}
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/5 to-transparent rounded-full blur-xl -z-10 transform translate-x-1/3 -translate-y-1/3"></div>
                    <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-primary/5 to-transparent rounded-full blur-xl -z-10 transform -translate-x-1/3 translate-y-1/3"></div>

                    <div className="flex items-center gap-2 mb-6">
                      <div className="bg-primary/10 p-1.5 rounded-md">
                        <Palette className="h-4 w-4 text-primary" />
                      </div>
                      <h3 className="text-base font-medium">Theme</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div
                        className={`flex flex-col items-center justify-center p-6 rounded-xl border transition-all duration-300 cursor-pointer hover:shadow-md ${theme === 'light'
                          ? 'bg-white border-primary/30 shadow-sm ring-2 ring-primary/20'
                          : 'bg-white/90 border-border/50 hover:border-border'}`}
                        onClick={() => theme !== 'light' && toggleTheme()}
                      >
                        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-amber-400 to-amber-300 flex items-center justify-center mb-3 shadow-sm">
                          <Sun className="h-8 w-8 text-amber-50" />
                        </div>
                        <h4 className="text-sm font-medium text-gray-900">Light Mode</h4>
                        <p className="text-xs text-gray-500 mt-1 text-center">
                          Bright theme for daytime use
                        </p>
                        {theme === 'light' && (
                          <div className="mt-3 bg-primary/10 text-primary text-xs px-2 py-1 rounded-full font-medium">
                            Active
                          </div>
                        )}
                      </div>

                      <div
                        className={`flex flex-col items-center justify-center p-6 rounded-xl border transition-all duration-300 cursor-pointer hover:shadow-md ${theme === 'dark'
                          ? 'bg-gray-900 border-primary/30 shadow-sm ring-2 ring-primary/20'
                          : 'bg-gray-900/90 border-border/50 hover:border-border'}`}
                        onClick={() => theme !== 'dark' && toggleTheme()}
                      >
                        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-indigo-900 to-purple-900 flex items-center justify-center mb-3 shadow-sm">
                          <Moon className="h-8 w-8 text-indigo-100" />
                        </div>
                        <h4 className="text-sm font-medium text-gray-100">Dark Mode</h4>
                        <p className="text-xs text-gray-400 mt-1 text-center">
                          Darker theme for reduced eye strain
                        </p>
                        {theme === 'dark' && (
                          <div className="mt-3 bg-primary/20 text-primary-foreground text-xs px-2 py-1 rounded-full font-medium">
                            Active
                          </div>
                        )}
                      </div>
                    </div>

                    <p className="text-xs text-muted-foreground mt-4 text-center">
                      Your theme preference will be saved and applied across all your devices.
                    </p>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="data" className="m-0 mt-6 md:mt-0">
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-semibold tracking-tight mb-1 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">Data</h2>
                    <div className="h-1 w-12 bg-gradient-to-r from-primary to-primary/30 rounded-full mb-3"></div>
                    <p className="text-muted-foreground">
                      Manage your personal data and privacy settings.
                    </p>
                  </div>

                  <div className="rounded-xl border border-border/40 p-6 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm shadow-sm relative overflow-hidden">
                    {/* Decorative elements */}
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/5 to-transparent rounded-full blur-xl -z-10 transform translate-x-1/3 -translate-y-1/3"></div>
                    <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-primary/5 to-transparent rounded-full blur-xl -z-10 transform -translate-x-1/3 translate-y-1/3"></div>

                    <div className="flex items-center gap-2 mb-6">
                      <div className="bg-primary/10 p-1.5 rounded-md">
                        <MessageSquare className="h-4 w-4 text-primary" />
                      </div>
                      <h3 className="text-base font-medium">Chat History</h3>
                    </div>

                    <div className="bg-background/60 p-5 rounded-lg border border-border/30 shadow-sm">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium flex items-center gap-1.5">
                            <Trash2 className="h-3.5 w-3.5 text-destructive/70" />
                            Delete All Chat Conversations
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            This will permanently remove all your AI chat history from our servers.
                          </p>
                          <div className="flex items-center mt-1 text-xs text-amber-600 dark:text-amber-400 bg-amber-50/50 dark:bg-amber-900/10 p-2 rounded-md border border-amber-100/50 dark:border-amber-400/20">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1.5">
                              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                              <line x1="12" y1="9" x2="12" y2="13"></line>
                              <line x1="12" y1="17" x2="12.01" y2="17"></line>
                            </svg>
                            This action cannot be undone
                          </div>
                        </div>

                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="destructive"
                              size="sm"
                              className="bg-destructive/90 hover:bg-destructive transition-all duration-200 shadow-sm"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete All Chats
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent className="max-w-md border-destructive/20">
                            <AlertDialogHeader>
                              <AlertDialogTitle className="text-destructive flex items-center gap-2">
                                <Trash2 className="h-5 w-5" />
                                Are you absolutely sure?
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                This action cannot be undone. This will permanently delete all your chat history and remove the data from our servers.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel className="transition-all duration-200">Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={handleDeleteChats}
                                className="bg-destructive hover:bg-destructive/90 transition-all duration-200"
                                disabled={isUpdating}
                              >
                                {isUpdating ? (
                                  <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Deleting...
                                  </>
                                ) : (
                                  <>
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete All Chats
                                  </>
                                )}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>

                    <p className="text-xs text-muted-foreground mt-4 text-center">
                      Your data is securely stored and never shared with third parties.
                    </p>
                  </div>
                </div>
              </TabsContent>


            </div>
          </div>
        </Tabs>
      </div>
    </div>
  );
};

export default SettingsPage;