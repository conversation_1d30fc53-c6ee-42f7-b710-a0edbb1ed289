import React, { useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext"
import { EnhancedTaskManagement } from "@/components/enhanced-tasks/EnhancedTaskManagement"
import { SmallDeviceWarning } from '@/components/ui/SmallDeviceWarning'
import { useDocumentTitle } from "@/hooks/useDocumentTitle"
import Header from "@/components/shared/Header"
import { motion } from "framer-motion"

export default function Tasks() {
  useDocumentTitle("Tasks - IsotopeAI");
  const { user, loading } = useSupabaseAuth()
  const navigate = useNavigate()

  useEffect(() => {
    if (!loading && !user) {
      navigate('/login')
    }
  }, [user, loading, navigate])

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-[#0a0f2c] dark:to-[#1a1f3c]">
        <div className="relative w-16 h-16">
          <div className="absolute top-0 left-0 w-full h-full rounded-full border-4 border-primary/30 animate-pulse"></div>
          <div className="absolute top-0 left-0 w-full h-full rounded-full border-t-4 border-primary animate-spin"></div>
        </div>
        <p className="mt-4 text-muted-foreground animate-pulse">Loading your workspace...</p>
      </div>
    )
  }

  return (
    <div className="relative min-h-screen w-full text-gray-900 dark:text-white">
      {/* Animated Gradient Background */}
      <motion.div
        className="absolute inset-0 -z-10"
        animate={{
          background: [
            "radial-gradient(ellipse 80% 80% at 50% -20%, rgba(120, 119, 198, 0.3), rgba(255, 255, 255, 0))",
            "radial-gradient(ellipse 80% 80% at 50% -20%, rgba(119, 198, 120, 0.3), rgba(255, 255, 255, 0))",
            "radial-gradient(ellipse 80% 80% at 50% -20%, rgba(198, 119, 120, 0.3), rgba(255, 255, 255, 0))",
            "radial-gradient(ellipse 80% 80% at 50% -20%, rgba(120, 119, 198, 0.3), rgba(255, 255, 255, 0))",
          ],
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          repeatType: "mirror",
        }}
      />
      <div className="absolute inset-0 -z-20 bg-neutral-50 dark:bg-neutral-950" />
      <SmallDeviceWarning />

      {/* Header */}
      <Header />

      {/* Main content with enhanced task management */}
      <motion.div
        className="relative z-10 flex flex-col min-h-screen pt-20 px-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <div className="flex-1 w-full max-w-7xl mx-auto">
          {/* Enhanced Task Management Component */}
          <motion.div
            className="min-h-[calc(100vh-120px)]"
            initial={{ opacity: 0, scale: 0.98 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <EnhancedTaskManagement />
          </motion.div>
        </div>
      </motion.div>
    </div>
  )
}
