// Test utility to verify task persistence functionality
import { taskStorage } from './taskLocalStorage';
import { EnhancedTodoItem } from '@/types/todo';

export const testTaskPersistence = () => {
  const testUserId = 'test_user_123';
  
  console.log('🧪 Testing task persistence...');
  
  // Clear any existing test data
  try {
    const storageKey = `isotope_tasks_${testUserId}`;
    localStorage.removeItem(storageKey);
    console.log('✅ Cleared existing test data');
  } catch (error) {
    console.error('❌ Failed to clear test data:', error);
  }
  
  // Test 1: Save a task
  const testTask: EnhancedTodoItem = {
    id: 'test_task_1',
    title: 'Test Task for Persistence',
    description: 'This is a test task to verify localStorage persistence',
    priority: 'high',
    createdAt: Date.now(),
    updatedAt: Date.now(),
    createdBy: testUserId,
    columnId: 'column-1',
    tags: ['test', 'persistence'],
    chapterTags: ['chapter1'],
    difficultyLevel: 'medium',
    completionPercentage: 0,
    viewCount: 0,
  };
  
  try {
    const savedTask = taskStorage.saveTask(testUserId, testTask);
    console.log('✅ Task saved successfully:', savedTask.title);
  } catch (error) {
    console.error('❌ Failed to save task:', error);
    return false;
  }
  
  // Test 2: Retrieve the task
  try {
    const retrievedTasks = taskStorage.getAllTasks(testUserId);
    if (retrievedTasks.length === 1 && retrievedTasks[0].id === testTask.id) {
      console.log('✅ Task retrieved successfully:', retrievedTasks[0].title);
    } else {
      console.error('❌ Task retrieval failed. Expected 1 task, got:', retrievedTasks.length);
      return false;
    }
  } catch (error) {
    console.error('❌ Failed to retrieve tasks:', error);
    return false;
  }
  
  // Test 3: Test import functionality
  const importData = {
    exportedAt: new Date().toISOString(),
    version: '1.0',
    tasks: [
      {
        id: 'imported_task_1',
        title: 'Imported Test Task',
        description: 'This task was imported from JSON',
        priority: 'medium',
        createdAt: Date.now(),
        updatedAt: Date.now(),
        createdBy: testUserId,
        columnId: 'column-2',
        tags: ['imported'],
        chapterTags: [],
        difficultyLevel: 'easy',
        completionPercentage: 50,
        viewCount: 0,
      }
    ]
  };
  
  try {
    const importResult = taskStorage.importTasks(testUserId, JSON.stringify(importData));
    if (importResult.success && importResult.imported === 1) {
      console.log('✅ Import test successful. Imported:', importResult.imported, 'tasks');
    } else {
      console.error('❌ Import test failed:', importResult);
      return false;
    }
  } catch (error) {
    console.error('❌ Import test failed with error:', error);
    return false;
  }
  
  // Test 4: Verify both tasks exist
  try {
    const allTasks = taskStorage.getAllTasks(testUserId);
    if (allTasks.length === 2) {
      console.log('✅ Both tasks exist after import. Total tasks:', allTasks.length);
      allTasks.forEach(task => console.log('  - Task:', task.title));
    } else {
      console.error('❌ Expected 2 tasks after import, got:', allTasks.length);
      return false;
    }
  } catch (error) {
    console.error('❌ Failed to verify tasks after import:', error);
    return false;
  }
  
  // Test 5: Test localStorage persistence across "page refresh" simulation
  try {
    // Simulate what happens on page refresh by getting fresh data from localStorage
    const tasksAfterRefresh = taskStorage.getAllTasks(testUserId);
    if (tasksAfterRefresh.length === 2) {
      console.log('✅ Tasks persist after simulated page refresh');
    } else {
      console.error('❌ Tasks lost after simulated refresh. Found:', tasksAfterRefresh.length);
      return false;
    }
  } catch (error) {
    console.error('❌ Persistence test failed:', error);
    return false;
  }
  
  // Cleanup
  try {
    const storageKey = `isotope_tasks_${testUserId}`;
    localStorage.removeItem(storageKey);
    console.log('✅ Test cleanup completed');
  } catch (error) {
    console.error('❌ Failed to cleanup test data:', error);
  }
  
  console.log('🎉 All persistence tests passed!');
  return true;
};

// Function to test the actual store persistence
export const testStorePersistence = async () => {
  console.log('🧪 Testing store persistence...');

  // This would need to be called from a component that has access to the store
  // For now, just log instructions
  console.log('To test store persistence:');
  console.log('1. Import some tasks');
  console.log('2. Check browser localStorage for "enhanced-todo-store"');
  console.log('3. Refresh the page');
  console.log('4. Verify tasks are still visible');

  // Check if the store data exists in localStorage
  try {
    const storeData = localStorage.getItem('enhanced-todo-store');
    if (storeData) {
      const parsed = JSON.parse(storeData);
      console.log('✅ Store data found in localStorage');
      console.log('Store data keys:', Object.keys(parsed.state || {}));
      if (parsed.state?.board?.tasks) {
        const taskCount = Object.keys(parsed.state.board.tasks).length;
        console.log('Tasks in store:', taskCount);
      }
    } else {
      console.log('ℹ️ No store data found in localStorage (this is normal if no tasks have been created)');
    }
  } catch (error) {
    console.error('❌ Error checking store data:', error);
  }
};

// Function to test import with sample data
export const testImportSampleData = async () => {
  console.log('🧪 Testing import with sample data...');

  const sampleData = {
    "exportedAt": "2025-01-07T12:00:00.000Z",
    "version": "1.0",
    "tasks": [
      {
        "id": "sample_task_1",
        "title": "Sample Math Problem",
        "description": "Solve quadratic equations",
        "priority": "high",
        "createdAt": Date.now(),
        "updatedAt": Date.now(),
        "createdBy": "test_user",
        "columnId": "column-1",
        "tags": ["math", "algebra"],
        "chapterTags": ["quadratic-equations"],
        "difficultyLevel": "medium",
        "completionPercentage": 0,
        "viewCount": 0
      },
      {
        "id": "sample_task_2",
        "title": "Sample Physics Lab",
        "description": "Complete pendulum experiment",
        "priority": "medium",
        "createdAt": Date.now(),
        "updatedAt": Date.now(),
        "createdBy": "test_user",
        "columnId": "column-2",
        "tags": ["physics", "lab"],
        "chapterTags": ["mechanics"],
        "difficultyLevel": "easy",
        "completionPercentage": 50,
        "viewCount": 1
      }
    ]
  };

  try {
    // Check if we have access to the store
    if (typeof window !== 'undefined' && (window as any).testImportTasks) {
      const result = await (window as any).testImportTasks(JSON.stringify(sampleData));
      console.log('✅ Sample data import result:', result);
      return result;
    } else {
      console.log('❌ Store import function not available. Make sure you are on the tasks page.');
      return false;
    }
  } catch (error) {
    console.error('❌ Error importing sample data:', error);
    return false;
  }
};

// Add this to window for easy access
if (typeof window !== 'undefined') {
  (window as any).testImportSampleData = testImportSampleData;
}
