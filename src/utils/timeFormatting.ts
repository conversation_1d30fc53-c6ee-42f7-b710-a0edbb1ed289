// Time formatting utilities for study analytics

/**
 * Format seconds into human-readable time format (e.g., "2h 45m", "45m", "30s")
 * @param seconds - Time in seconds
 * @param options - Formatting options
 * @returns Formatted time string
 */
export const formatStudyTime = (
  seconds: number, 
  options: {
    showSeconds?: boolean;
    shortFormat?: boolean;
    showZeroMinutes?: boolean;
  } = {}
): string => {
  const { showSeconds = false, shortFormat = true, showZeroMinutes = false } = options;

  if (seconds === 0) {
    return showZeroMinutes ? '0m' : '0';
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  const parts: string[] = [];

  if (hours > 0) {
    parts.push(shortFormat ? `${hours}h` : `${hours} hour${hours !== 1 ? 's' : ''}`);
  }

  if (minutes > 0 || (hours === 0 && remainingSeconds === 0) || showZeroMinutes) {
    parts.push(shortFormat ? `${minutes}m` : `${minutes} minute${minutes !== 1 ? 's' : ''}`);
  }

  if (showSeconds && remainingSeconds > 0 && hours === 0) {
    parts.push(shortFormat ? `${remainingSeconds}s` : `${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`);
  }

  return parts.join(' ');
};

/**
 * Format time for display in analytics (always shows hours and minutes)
 * @param seconds - Time in seconds
 * @returns Formatted time string like "2h 45m"
 */
export const formatAnalyticsTime = (seconds: number): string => {
  if (seconds === 0) return '0m';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  
  return `${minutes}m`;
};

/**
 * Format time for compact display (e.g., in cards or small spaces)
 * @param seconds - Time in seconds
 * @returns Compact formatted time string
 */
export const formatCompactTime = (seconds: number): string => {
  if (seconds === 0) return '0m';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (hours > 0) {
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }
  
  return `${minutes}m`;
};

/**
 * Format duration for detailed display with seconds
 * @param seconds - Time in seconds
 * @returns Detailed formatted time string
 */
export const formatDetailedTime = (seconds: number): string => {
  return formatStudyTime(seconds, { showSeconds: true, shortFormat: true });
};

/**
 * Format time for streak display (days, hours, minutes)
 * @param seconds - Time in seconds
 * @returns Formatted time string for streaks
 */
export const formatStreakTime = (seconds: number): string => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  const parts: string[] = [];

  if (days > 0) {
    parts.push(`${days}d`);
  }
  if (hours > 0) {
    parts.push(`${hours}h`);
  }
  if (minutes > 0 || parts.length === 0) {
    parts.push(`${minutes}m`);
  }

  return parts.join(' ');
};

/**
 * Convert seconds to decimal hours for calculations
 * @param seconds - Time in seconds
 * @returns Time in decimal hours
 */
export const secondsToHours = (seconds: number): number => {
  return seconds / 3600;
};

/**
 * Convert decimal hours to seconds
 * @param hours - Time in decimal hours
 * @returns Time in seconds
 */
export const hoursToSeconds = (hours: number): number => {
  return Math.round(hours * 3600);
};

/**
 * Format percentage change with appropriate sign and color indication
 * @param percentage - Percentage change value
 * @returns Object with formatted text and trend direction
 */
export const formatTrendPercentage = (percentage: number): {
  text: string;
  trend: 'up' | 'down' | 'neutral';
  color: string;
} => {
  const absPercentage = Math.abs(percentage);
  const sign = percentage > 0 ? '+' : percentage < 0 ? '-' : '';
  
  let trend: 'up' | 'down' | 'neutral' = 'neutral';
  let color = 'text-gray-500';

  if (percentage > 0) {
    trend = 'up';
    color = 'text-green-500';
  } else if (percentage < 0) {
    trend = 'down';
    color = 'text-red-500';
  }

  return {
    text: `${sign}${absPercentage.toFixed(1)}%`,
    trend,
    color
  };
};

/**
 * Format date for display in analytics
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Formatted date string
 */
export const formatAnalyticsDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
};

/**
 * Format date for compact display
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Compact formatted date string
 */
export const formatCompactDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  });
};

/**
 * Get relative time description (e.g., "today", "yesterday", "3 days ago")
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Relative time description
 */
export const getRelativeTimeDescription = (dateString: string): string => {
  const date = new Date(dateString);
  const today = new Date();
  const diffTime = today.getTime() - date.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) return 'today';
  if (diffDays === 1) return 'yesterday';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks} week${weeks !== 1 ? 's' : ''} ago`;
  }
  if (diffDays < 365) {
    const months = Math.floor(diffDays / 30);
    return `${months} month${months !== 1 ? 's' : ''} ago`;
  }
  
  const years = Math.floor(diffDays / 365);
  return `${years} year${years !== 1 ? 's' : ''} ago`;
};

/**
 * Format milestone text for streak achievements
 * @param milestone - Milestone number (days)
 * @returns Formatted milestone text
 */
export const formatMilestoneText = (milestone: number): string => {
  if (milestone < 7) return `${milestone} days`;
  if (milestone < 30) {
    const weeks = Math.floor(milestone / 7);
    const remainingDays = milestone % 7;
    if (remainingDays === 0) return `${weeks} week${weeks !== 1 ? 's' : ''}`;
    return `${weeks}w ${remainingDays}d`;
  }
  if (milestone < 365) {
    const months = Math.floor(milestone / 30);
    const remainingDays = milestone % 30;
    if (remainingDays === 0) return `${months} month${months !== 1 ? 's' : ''}`;
    return `${months}m ${remainingDays}d`;
  }
  
  const years = Math.floor(milestone / 365);
  const remainingDays = milestone % 365;
  if (remainingDays === 0) return `${years} year${years !== 1 ? 's' : ''}`;
  return `${years}y ${remainingDays}d`;
};

/**
 * Calculate and format study efficiency (study time vs available time)
 * @param studySeconds - Time spent studying in seconds
 * @param availableHours - Available hours for studying
 * @returns Efficiency percentage and description
 */
export const calculateStudyEfficiency = (studySeconds: number, availableHours: number = 8): {
  percentage: number;
  description: string;
  color: string;
} => {
  const availableSeconds = availableHours * 3600;
  const percentage = Math.min((studySeconds / availableSeconds) * 100, 100);
  
  let description = '';
  let color = '';

  if (percentage >= 80) {
    description = 'Excellent';
    color = 'text-green-600';
  } else if (percentage >= 60) {
    description = 'Good';
    color = 'text-blue-600';
  } else if (percentage >= 40) {
    description = 'Fair';
    color = 'text-yellow-600';
  } else if (percentage >= 20) {
    description = 'Needs Improvement';
    color = 'text-orange-600';
  } else {
    description = 'Poor';
    color = 'text-red-600';
  }

  return {
    percentage: Math.round(percentage),
    description,
    color
  };
};
